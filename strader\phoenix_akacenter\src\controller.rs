use crate::config::settings::Settings;
// use crate::dataservice::entities::users_agent::Model;
use akaclient::akaclient::get_currency_by_code;
use anyhow::Result;
use chrono::{Duration, Local, NaiveDate};
use protoes::phoenixakacenter::*;
use std::cmp::Ordering;
use std::collections::{BTreeMap, HashMap};
use std::fmt::Debug;
// use std::io::Write;
use std::sync::{Arc, RwLock};
// use rust_decimal::{prelude::*, Decimal};
use crate::app::constdata;
use tonic::{self, Status};
// use utility::{constant, errors, errors::ErrorCode};
use crate::app::constdata::{QueryType, TradingDayType};
use crate::dataservice::customer_entities::prelude::UsersTradeAccount;
use crate::dataservice::dataaccess::sys_commodity::CommoditySelectRes;
use crate::dataservice::dataaccess::sys_stock_suspension_record::StockSuspensionRecordSelectRes;
// use crate::dataservice::dataaccess::sys_trade_config_commodity::SysTradeConfigCommoditySelectRes;
use crate::dataservice::dbsetup::DbConnection;
use crate::dataservice::entities::prelude::{
    SysCommodity, SysCommodityChannel, SysCommodityExt, SysMarket, SysStockSuspensionRecord, SysTradeChannel, SysTradeDateConfig, UsersAgentTrading, UsersChinextRate, UsersLevelRecord, UsersRq,
};
use crate::dataservice::entities::sys_market::CurrencyType;
use crate::dataservice::entities::{sys_commodity, sys_commodity_channel, sys_trade_date_config};
// use crate::dataservice::stock_entities::phoenix_oms_feeset;
use crate::dataservice::stock_entities::prelude::PhoenixSysSystem;
use crate::service::fee_setting::FeeSettingCache;
use async_recursion::async_recursion;
use common::redisclient::redispool::RedisClient;
use itertools::Itertools;
// use {error, info};
use tracing::*;
// use rayon::iter::ParallelIterator;
// use rayon::prelude::{IndexedParallelIterator, IntoParallelRefIterator};
use sea_orm::{ColumnTrait, Condition};
use serde::{Deserialize, Serialize};
use tokio::join;

#[macro_export]
macro_rules! result {
    ($($a: expr) ,*) => {
        ($(
        match $a {
            Ok(c) => c,
            Err(e) => {
                // error!("{}", e);
                return Err(Status::unknown(format!("{:?}", e)));
            }
        }
        ), *)
    };
}

#[derive(Serialize, Deserialize, Debug)]
struct TradeChannelConfig {
    id: i64,
    #[serde(rename = "tradeConfigId")]
    trade_config_id: i64,
    #[serde(rename = "tradeChannelId")]
    trade_channel_id: i64,
    level: i32,
    state: i32,
    #[serde(rename = "userId")]
    user_id: i64,
    #[serde(rename = "updateTime")]
    update_time: i32,
    direction: i32,
}

#[derive(Serialize, Deserialize, Debug)]
struct StockChannelInfoJson {
    #[serde(rename = "channelId")]
    channel_id: i64,
    #[serde(rename = "channelName")]
    channel_name: String,
    #[serde(rename = "commodityId")]
    commodity_id: i64,
    #[serde(rename = "maxHoldNum")]
    max_hold_num: i64,
    #[serde(rename = "maxHoldValue")]
    max_hold_value: i64,
}

// #[derive(Clone)]
pub struct AkaServerController {
    pub settings: Settings,
    pub redis: RedisClient,
    pub fee_setting_cache: FeeSettingCache,
    pub customer_db: DbConnection,
    pub finances_db: DbConnection,
    pub stock_db: DbConnection,
    pub(crate) redis_on: bool,
}

impl AkaServerController {
    async fn get_rate(&self, key: &str) -> Result<(f64, i64), Status> {
        let value = self.redis.get_value_by_get(&key).await;
        if !value.is_empty() {
            let vstr: Vec<&str> = value.split("|").collect();

            let rate = vstr[0].parse().unwrap_or_default();
            let modify_time = vstr[1].parse().unwrap_or_default();
            Ok((rate, modify_time))
        } else {
            error!("get_value_by_get 没有找到数据{}", &key);
            Err(Status::unknown(format!("get_value_by_get 没有找到数据{}", &key)))
        }
    }

    #[async_recursion]
    async fn find_day_type_from_list(&self, key: &str, date: i64, mut index: i32, list_len: i32) -> Result<TradingDayType, Status> {
        if index >= list_len {
            error!("没有找到对应交易日信息: {}", date);
            return Err(Status::not_found(format!("没有找到对应交易日信息: {}", date)));
        }
        let holiday_date_info = self.redis.get_values_by_lindex(key, index).await;

        let vstr: Vec<&str> = holiday_date_info.split(",").collect();
        if vstr.len() != 2 {
            error!("交易日数据格式不正确: {} {}", key, index);
            return Err(Status::unknown(format!("交易日数据格式不正确: {} {}", key, index)));
        }

        let holiday_date = vstr[0].parse::<i64>().unwrap_or_default();
        if holiday_date == 0 {
            error!("交易日数据格式不正确: {} {}", key, index);
            return Err(Status::unknown(format!("交易日数据格式不正确: {} {}", key, index)));
        }

        return match holiday_date.cmp(&date) {
            //比较日期
            Ordering::Less => {
                //读取日期比当前小
                index += 1;
                self.find_day_type_from_list(key, date, index, list_len).await
            }
            Ordering::Equal => {
                //读取到当前日期
                if let Ok(market_type) = vstr[1].parse::<i32>() {
                    if market_type < 0 || market_type > 2 {
                        error!("交易日类型不正确: {} {}", key, index);
                        return Err(Status::unknown(format!("交易日类型不正确: {} {}", key, index)));
                    }
                    match market_type.try_into() {
                        Ok(TradingDayType::NonTradingDay) => Ok(TradingDayType::NonTradingDay),
                        Ok(TradingDayType::TradingDay) => Ok(TradingDayType::TradingDay),
                        Ok(TradingDayType::HalfTradingDay) => Ok(TradingDayType::HalfTradingDay),
                        Err(_) => {
                            error!("交易日类型不正确: {} {}", key, index);
                            Err(Status::unknown(format!("交易日类型不正确: {} {}", key, index)))
                        }
                    }
                } else {
                    error!("交易日类型不正确: {} {}", key, index);
                    Err(Status::unknown(format!("交易日类型不正确: {} {}", key, index)))
                }
            }
            Ordering::Greater => Ok(TradingDayType::NonTradingDay), //读取日期比当前日期大
        };
    }

    async fn get_date_type(&self, market_id: i64, date: i64) -> Result<TradingDayType, Status> {
        let key = format!("{}{}", constdata::HOLIDAY_MARKET_KEY, market_id);
        let list_len = result!(self.redis.get_len(&key).await);

        if list_len <= 0 {
            error!("没有找到任何交易日");
            return Err(Status::not_found("没有找到任何交易日"));
        }

        let index = 0;
        return self.find_day_type_from_list(&key, date, index, list_len as i32).await;
    }

    async fn write_redis_hash<'a, T: Serialize + Deserialize<'a>>(&self, key: &str, model_datum: &T) -> Result<(), Status> {
        let json_data = serde_json::to_string(model_datum).unwrap_or_default();
        if json_data.is_empty() {
            return Err(Status::unknown(format!("结构体解析失败: {}", &key)));
        }
        let json_data = json_data.replace(" ", "").replace("\"", "").replace(",", " ").replace(":", " ").replace("{", "").replace("}", "");
        let vstr: Vec<&str> = json_data.split(" ").collect();

        let arg_list = vstr
            .iter()
            .batching(|it| match it.next() {
                None => None,
                Some(x) => match it.next() {
                    None => None,
                    Some(y) => Some((x, y)),
                },
            })
            .collect::<Vec<(&&str, &&str)>>();

        // println!("{:?}", arg_list);

        if self.redis.set_hash_value(&key, arg_list).await.is_err() {
            error!("redis 写入失败: {}", &key);
            Err(Status::unknown(format!("redis 写入失败: {}", &key)))
        } else {
            // info!("redis 写入成功: {}", &key);
            Ok(())
        }
    }

    fn get_current_date(&self) -> i64 {
        let current_date_str = Local::now().format("%Y%m%d").to_string();
        current_date_str.parse::<i64>().unwrap_or_default()
    }
}

//处理业务逻辑
impl AkaServerController {
    /// 股票通道最大持仓量等信息(需本地缓存，消息通知更新)
    pub async fn query_channel_hold_limit(&self, req: ChannelHoldLimitReq) -> Result<ChannelHoldLimitResp, Status> {
        // info!("query_channel_hold_limit 查询股票通道最大持仓量等信息 start");
        // info!("query_channel_hold_limit,输入请求为{:?}", &req);

        if req.stock_id == 0 {
            //stock_id不能为0
            return Err(Status::invalid_argument(format!("stock_id 不能为空")));
        }

        //查询整体最大持仓量
        let key = format!("{}{}", constdata::COMMODITY_KEY, req.stock_id);
        let total_max_hold = if self.redis.get_key_is_exists(&key).await {
            let max_hold_str = self.redis.get_value_by_hget(&key, "MaxHold").await;
            result!(max_hold_str.parse::<i64>())
        } else {
            return Err(Status::invalid_argument(format!("{} 不存在", &key)));
        };

        //查询通道信息
        let key = format!("{}{}", constdata::QUERY_COMMODITY_CHANNLE_CONFIG_KEY, req.stock_id);
        let vec_channel_hold_limit = if self.redis_on && self.redis.get_key_is_exists(&key).await {
            let value = self.redis.get_value_by_get(&key).await;
            let ret_vec_stock_channle_info: Result<Vec<StockChannelInfoJson>, String> = serde_json::from_str(&value).map_err(|e| format!("get_value_by_get 数据解析失败{}", e));
            if !value.is_empty() {
                let mut vec_channel_hold_limit = Vec::<ChannelHoldLimit>::new();
                let vec_stock_channle_info = result!(ret_vec_stock_channle_info);
                for stock_channle_info in &vec_stock_channle_info {
                    if req.channel_id > 0 && stock_channle_info.channel_id != req.channel_id {
                        continue;
                    }
                    let data = ChannelHoldLimit {
                        channel_id: stock_channle_info.channel_id,
                        stock_id: req.stock_id,
                        max_holdnum: stock_channle_info.max_hold_num,
                        max_holdvalue: stock_channle_info.max_hold_value,
                    };

                    vec_channel_hold_limit.push(data);
                }
                vec_channel_hold_limit
            } else {
                return Err(Status::not_found(format!("get_value_by_get 没有找到数据{}", &key)));
            }
        } else {
            //如果redis内不存在，则从数据库查找
            let condition = match req.channel_id {
                0 => {
                    //channel_id为0时，查询所有产品
                    Condition::all().add(sys_commodity_channel::Column::CommodityId.eq(req.stock_id))
                }
                _ => Condition::all()
                    .add(sys_commodity_channel::Column::ChannelId.eq(req.channel_id))
                    .add(sys_commodity_channel::Column::CommodityId.eq(req.stock_id)),
            };
            let future_sys_commodity_channel = SysCommodityChannel::find_by_condition(&self.finances_db, condition); //产品通道信息
            let future_model_data = SysTradeChannel::find_all(&self.finances_db); //交易通道信息
            let (re_sys_commodity_channel, re_model_data): (Result<Vec<SysCommodityChannel>>, Result<Vec<SysTradeChannel>>) = join!(future_sys_commodity_channel, future_model_data);
            let (vec_sys_commodity_channel, vec_model_data) = result!(re_sys_commodity_channel, re_model_data);

            //如果产品通道信息为空
            // let config_commodity = result!(SysTradeConfigCommodity::find_by_commodity_id(&self.finances_db, req.stock_id).await); //交易产品配置信息
            // let vec_config_channel = result!(SysTradeConfigChannel::find_by_trade_config_id(&self.finances_db, config_commodity.trade_config_id).await); //交易通道配置信息

            let vec_stock_channel_info: Vec<_> = vec_model_data
                .iter()
                .filter_map(|channel_id| {
                    if (req.channel_id > 0 && channel_id.id == req.channel_id) || req.channel_id == 0 {
                        //请求的channel_id为0时，返回全部，否则返回对应
                        match vec_sys_commodity_channel.iter().find(|sys_commodity_channel| sys_commodity_channel.channel_id == channel_id.id) {
                            None => Some(StockChannelInfoJson {
                                channel_id: channel_id.id,
                                channel_name: channel_id.name.clone(),
                                commodity_id: req.stock_id,
                                max_hold_num: 100000000,
                                max_hold_value: 10000000,
                            }),
                            Some(sys_commodity_channel) => Some(StockChannelInfoJson {
                                channel_id: channel_id.id,
                                channel_name: channel_id.name.clone(),
                                commodity_id: req.stock_id,
                                max_hold_num: sys_commodity_channel.max_num,
                                max_hold_value: sys_commodity_channel.max_value,
                            }),
                        }
                    } else {
                        None
                    }
                })
                .collect();

            if self.redis_on {
                let j = result!(serde_json::to_string(&vec_stock_channel_info));
                result!(self.redis.set_str_value(&key, constdata::EXPARE_TIME_1_DAY, &j).await);
                // info!("{} redis写入成功", &key);
            }

            vec_stock_channel_info
                .iter()
                .map(|stock_channel_info| ChannelHoldLimit {
                    channel_id: stock_channel_info.channel_id,
                    stock_id: stock_channel_info.commodity_id,
                    max_holdnum: stock_channel_info.max_hold_num,
                    max_holdvalue: stock_channel_info.max_hold_value,
                })
                .collect()
        };

        // info!("query_channel_hold_limit 查询股票通道最大持仓量等信息 end");

        Ok(ChannelHoldLimitResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_channel_hold_limit,
            // total_max_hold: total_max_hold.load(Ordering::Relaxed),
            total_max_hold,
        })
    }

    /// 通道基础信息(需本地缓存，消息通知更新)
    pub async fn query_channel_info(&self, req: ChannelInfoReq) -> Result<ChannelInfoResp, Status> {
        // info!("query_channel_info 查询通道基础信息 start");
        // info!("query_channel_info,输入请求为{:?}", &req);

        let mut err_flag = true;
        let mut vec_channel_info = Vec::<ChannelInfo>::new();

        let mut map2channel_info = |rmap: BTreeMap<String, String>| -> ChannelInfo {
            let mut channel_info = ChannelInfo {
                channel_id: 0,
                channel_name: "".to_string(),
                channel_state: 0,
                channel_type: 0,
                account_id: 0,
                qfii_state: 0,
                channel_attr: 1,
            };

            if !rmap.is_empty() {
                if let Some(channel_id_str) = rmap.get("channel_id") {
                    channel_info.channel_id = channel_id_str.parse().unwrap_or_default();
                } else {
                    error!("channel_id not found");
                    err_flag = false;
                }
                if let Some(channel_name) = rmap.get("channel_name") {
                    channel_info.channel_name = channel_name.clone();
                } else {
                    error!("channel_name not found");
                    err_flag = false;
                }
                if let Some(channel_state_str) = rmap.get("channel_state") {
                    channel_info.channel_state = channel_state_str.parse().unwrap_or_default();
                } else {
                    error!("channel_state not found");
                    err_flag = false;
                }
                if let Some(channel_type_str) = rmap.get("channel_type") {
                    channel_info.channel_type = channel_type_str.parse().unwrap_or_default();
                } else {
                    error!("channel_type not found");
                    err_flag = false;
                }
                if let Some(account_id_str) = rmap.get("account_id") {
                    channel_info.account_id = account_id_str.parse().unwrap_or_default();
                } else {
                    error!("account_id not found");
                    err_flag = false;
                }
                if let Some(qfii_state_str) = rmap.get("qfii_state") {
                    if let Ok(qfii_state) = qfii_state_str.parse() {
                        channel_info.qfii_state = match qfii_state {
                            1 => qfii_state,
                            _ => 0,
                        };
                    }
                } else {
                    error!("qfii_state not found");
                    err_flag = false;
                }
            } else {
                error!("redis not found");
                err_flag = false;
            }

            channel_info
        };

        match req.channel_id {
            0 => {
                // channel_id为零，从数据库查出所有
                let vec_model_data: Vec<SysTradeChannel> = result!(SysTradeChannel::find_all(&self.finances_db).await); //交易通道信息

                for model_datum in vec_model_data {
                    let channel_info = ChannelInfo {
                        channel_id: model_datum.id,
                        channel_name: model_datum.name.clone(),
                        channel_state: model_datum.state,
                        channel_type: model_datum.inner_type as i32,
                        account_id: model_datum.unit_id,
                        qfii_state: match model_datum.qfstate {
                            1 => model_datum.qfstate as i32,
                            _ => 0,
                        },
                        channel_attr: model_datum.channel_attr,
                    };

                    if channel_info.account_id > 0 {
                        vec_channel_info.push(channel_info);
                    }
                }
            }
            _ => {
                let key = format!("{}{}", constdata::CHANNEL_KEY, req.channel_id);
                let channel_info = if self.redis_on && self.redis.get_key_is_exists(&key).await {
                    //如果redis存在
                    let rmap = self.redis.get_value_by_hgetall(&key).await;
                    map2channel_info(rmap)
                } else {
                    let model_data = result!(SysTradeChannel::find_by_id(&self.finances_db, req.channel_id).await);

                    let channel_info = ChannelInfo {
                        channel_id: model_data.id,
                        channel_name: model_data.name.clone(),
                        channel_state: model_data.state,
                        channel_type: model_data.inner_type as i32,
                        account_id: model_data.unit_id,
                        qfii_state: match model_data.qfstate {
                            1 => model_data.qfstate as i32,
                            _ => 0,
                        },
                        channel_attr: model_data.channel_attr,
                    };

                    if self.redis_on {
                        self.write_redis_hash(&key, &channel_info).await?;
                    }

                    channel_info
                };
                if channel_info.account_id > 0 {
                    vec_channel_info.push(channel_info);
                }
            }
        };

        if !err_flag {
            return Err(Status::not_found("some data cant be found from redis".to_string()));
        }

        // info!("query_channel_info 查询通道基础信息 :{:?}", &vec_channel_info);

        Ok(ChannelInfoResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_channel_info,
        })
    }

    /// 股票通道配置优先级信息
    pub async fn query_stock_channel(&self, req: StockChannelReq) -> Result<StockChannelResp, Status> {
        // info!("query_stock_channel 查询股票通道配置优先级信息 start");
        // info!("query_stock_channel 输入请求为{:?}", &req);

        let key = format!("commodity_tradeconfig_{}", req.stock_id);
        // info!("find key: \"{}\" in redis. ", &key);
        if !self.redis.get_key_is_exists(&key).await {
            return Ok(StockChannelResp {
                ret_code: -1,
                ret_msg: format!("not find key: {} in redis", &key),
                data: vec![],
            });
        }

        let trade_config_id = self.redis.get_value_by_get(&key).await;
        // info!("redis output: {}", &trade_config_id);

        let key = "tradeconfig_".to_string() + &trade_config_id;
        // info!("find by key: \"{}\" in redis. ", &key);
        if !self.redis.get_key_is_exists(&key).await {
            return Ok(StockChannelResp {
                ret_code: -1,
                ret_msg: format!("not find key: {} in redis", &key),
                data: vec![],
            });
        }

        let rmap = self.redis.get_value_by_hgetall(&key).await;
        let p_key1 = format!("_{}_{}", req.user_id, req.order_direction);
        let p_key2 = format!("_0_{}", req.order_direction);
        let keys: Vec<_> = rmap.keys().filter(|key| key.ends_with(&p_key1) || key.ends_with(&p_key2)).cloned().collect();
        // info!("找到{}条key", keys.len());

        let vec_model_data: Vec<SysTradeChannel> = result!(SysTradeChannel::find_all(&self.finances_db).await); //交易通道信息
        let mut map_id_name = HashMap::new(); //通道id -> 通道名
        vec_model_data.iter().for_each(|model_data| {
            map_id_name.insert(model_data.id, (model_data.name.clone(), model_data.inner_type, model_data.qfstate));
        });

        let vec_stock_channel: Vec<_> = keys
            .iter()
            .filter_map(|key| {
                if let Some(json_value) = rmap.get(key) {
                    let trade_channel_config = serde_json::from_str(&json_value);
                    if trade_channel_config.is_err() {
                        error!("{:?}", trade_channel_config.unwrap_err());
                        return None;
                    }
                    let trade_channel_config: TradeChannelConfig = trade_channel_config.unwrap();

                    let (channel_name, channel_type, qfii_state) = match map_id_name.get(&trade_channel_config.trade_channel_id) {
                        None => return None,
                        Some(tuple) => tuple,
                    };

                    Some(ChannelConfig {
                        id: trade_channel_config.id,
                        channel_id: trade_channel_config.trade_channel_id,
                        channel_name: channel_name.to_owned(),
                        user_id: trade_channel_config.user_id,
                        channel_level: trade_channel_config.level,
                        channel_type: (*channel_type) as i32,
                        channel_status: trade_channel_config.state,
                        commodity_group: trade_channel_config.trade_config_id,
                        qfii_state: match qfii_state {
                            1 => 1,
                            _ => 0,
                        },
                    })
                } else {
                    None
                }
            })
            .collect();

        // info!("query_stock_channel 查询股票通道配置信息结束:{:?}", &vec_stock_channel);

        Ok(StockChannelResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_stock_channel,
        })
    }

    /// 股票基础信息以及交易时间段
    pub async fn query_stock_info(&self, req: StockInfoReq) -> Result<StockInfoResp, Status> {
        // info!("query_stock_info 查询股票基础信息以及交易时间段 start");
        // info!("query_stock_info 输入请求为{:?}", &req);

        let mut err_flag = true;
        let mut vec_stock_info = Vec::<StockInfo>::new();

        let mut map2stock_info = |rmap: BTreeMap<String, String>| -> StockInfo {
            let mut stock_info = StockInfo {
                stock_id: 0,
                stock_code: "".to_string(),
                stock_name: "".to_string(),
                trade_state: 0,
                hands_num: "".to_string(),
                stock_type: 0,
                min_value: 0,
                max_value: 0,
                max_single_money: 0.0,
                market_id: 0,
                market_code: "".to_string(),
                trade_currency: 0,
                margin_rate: 1_f64,
            };

            if let Some(stock_id_str) = rmap.get("ID") {
                stock_info.stock_id = stock_id_str.parse().unwrap_or_default()
            } else {
                err_flag = false;
            }
            if let Some(code) = rmap.get("Code") {
                stock_info.stock_code = code.clone();
            } else {
                err_flag = false;
            }
            if let Some(name) = rmap.get("Name") {
                stock_info.stock_name = name.clone();
            } else {
                err_flag = false;
            }
            if let Some(trade_state_str) = rmap.get("TradeState") {
                stock_info.trade_state = trade_state_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(hands_num) = rmap.get("HandsNum") {
                stock_info.hands_num = hands_num.clone();
            } else {
                err_flag = false;
            }
            if let Some(stock_type_str) = rmap.get("StockType") {
                stock_info.stock_type = stock_type_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(min_value_str) = rmap.get("MinValue") {
                stock_info.min_value = min_value_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(max_value_str) = rmap.get("MaxValue") {
                stock_info.max_value = max_value_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(max_single_money_str) = rmap.get("MaxSingleMoney") {
                stock_info.max_single_money = max_single_money_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(market_id_str) = rmap.get("MarketID") {
                stock_info.market_id = market_id_str.parse().unwrap_or_default();
            } else {
                err_flag = false;
            }
            if let Some(market_code) = rmap.get("MarketCode") {
                stock_info.market_code = market_code.clone();
            } else {
                err_flag = false;
            }
            if let Some(trade_currency) = rmap.get("Currency") {
                stock_info.trade_currency = match trade_currency as &str {
                    constdata::CURRENCY_HKD => Currency::Hkd.into(),
                    constdata::CURRENCY_CNY => Currency::Cny.into(),
                    constdata::CURRENCY_USD => Currency::Usd.into(),
                    _ => {
                        error!("the trade currency is invalid: {}", trade_currency);
                        -1
                    }
                };
            } else {
                err_flag = false;
            }
            if let Some(margin_rate) = rmap.get("BondWc") {
                stock_info.margin_rate = margin_rate.parse().unwrap_or_default();
                if stock_info.margin_rate.eq(&0_f64) {
                    stock_info.margin_rate = 1.0;
                }
            } else {
                err_flag = false;
            }

            stock_info
        };

        match req.stock_id {
            -1 => {
                let condition = Condition::all().add(sys_commodity::Column::Code.eq(req.stock_code)).add(sys_commodity::Column::MarketId.eq(req.exchange_id));

                let sys_commodity = result!(SysCommodity::find_by_condition(&self.finances_db, condition).await);
                if sys_commodity.is_none() {
                    return Ok(StockInfoResp {
                        ret_code: -1,
                        ret_msg: "SysCommodity数据不存在".to_string(),
                        data: vec![],
                    });
                }
                let sys_commodity = sys_commodity.unwrap();
                let future_sys_market = SysMarket::find_by_id(&self.finances_db, req.exchange_id as i64);
                let future_sys_commodity_ext = SysCommodityExt::find_by_commodity_id(&self.finances_db, sys_commodity.id);
                let (re_sys_market, re_sys_commodity_ext): (Result<SysMarket>, Result<SysCommodityExt>) = join!(future_sys_market, future_sys_commodity_ext);
                let (sys_market, sys_commodity_ext) = result!(re_sys_market, re_sys_commodity_ext);

                // let sys_commodity: SysCommodity = result!(SysCommodity::find_by_condition(&self.finances_db, condition).await);
                // let sys_market: SysMarket = result!(SysMarket::find_by_id(&self.finances_db, req.exchange_id as i64).await);
                // let sys_commodity_ext: SysCommodityExt = result!(SysCommodityExt::find_by_commodity_id(&self.finances_db, sys_commodity.id).await);
                // let re_sys_trade_date_config: Result<SysTradeDateConfig> = SysTradeDateConfig::find_by_market_commodity_id(&self.finances_db, req.exchange_id, sys_commodity.id).await;
                // let sys_trade_date_config: SysTradeDateConfig = match re_sys_trade_date_config {
                //     Ok(sys_trade_date_config) => sys_trade_date_config,
                //     Err(_) => {
                //         result!(SysTradeDateConfig::find_by_market_commodity_id(&self.finances_db, req.exchange_id, 0).await)
                //     }
                // };

                let mut max_value = sys_commodity_ext.max_value.try_into().unwrap_or_default();
                let mut min_value = sys_commodity_ext.min_value.try_into().unwrap_or_default();
                let max_single_money = sys_commodity_ext.max_money as f64;
                let margin_rate: f64 = sys_commodity_ext.bond_wc.try_into().unwrap_or_default();
                let stock_code = sys_commodity.code;
                let hands_num = sys_commodity.hands_num;

                if min_value == 0 {
                    if sys_commodity.r#type == 1 {
                        min_value = hands_num
                    } else if sys_commodity.r#type == 3 {
                        if stock_code.len() > 3 && &stock_code.as_str()[0..3] == "688" {
                            min_value = 200;
                        } else {
                            min_value = 100;
                        }
                    }
                }

                if max_value == 0 {
                    if sys_commodity.r#type == 1 {
                        max_value = 2000000;
                    } else if sys_commodity.r#type == 3 {
                        if stock_code.len() > 3 && &stock_code.as_str()[0..3] == "688" {
                            max_value = 100000;
                        } else {
                            max_value = 300000;
                        }
                    }
                }

                if max_single_money == 0_f64 {
                    if sys_commodity.r#type == 1 {
                        max_value = 20000000;
                    } else if sys_commodity.r#type == 3 {
                        max_value = 3000000;
                    }
                }

                let stock_info = StockInfo {
                    stock_id: sys_commodity.id,
                    stock_code,
                    stock_name: sys_commodity.name,
                    trade_state: sys_commodity.trade_state as i32,
                    hands_num: sys_commodity.hands_num.to_string(),
                    stock_type: sys_commodity.stock_type as i32,
                    min_value,
                    max_value,
                    max_single_money,
                    market_id: sys_commodity.market_id,
                    market_code: sys_market.market_code,
                    trade_currency: match &sys_commodity.currency as &str {
                        constdata::CURRENCY_HKD => Currency::Hkd.into(),
                        constdata::CURRENCY_CNY => Currency::Cny.into(),
                        constdata::CURRENCY_USD => Currency::Usd.into(),
                        _ => {
                            error!("the trade currency is invalid: {}", &sys_commodity.currency);
                            -1
                        }
                    },
                    margin_rate: match margin_rate.total_cmp(&0_f64) {
                        Ordering::Equal => 1_f64,
                        _ => margin_rate,
                    },
                };

                vec_stock_info.push(stock_info);
            }
            0 => {
                let vec_commodity_info: Vec<CommoditySelectRes> = result!(SysCommodity::find_join_others(&self.finances_db).await);
                let vec_stock_info_ = Arc::new(RwLock::new(Vec::<StockInfo>::new()));
                rayon::scope(|s| {
                    for commodity_info in vec_commodity_info {
                        let vec_stock_info_ = vec_stock_info_.clone();
                        s.spawn(move |_| {
                            let mut max_value = commodity_info.max_value.try_into().unwrap_or_default();
                            let mut min_value = commodity_info.min_value.try_into().unwrap_or_default();
                            let max_single_money = commodity_info.max_money as f64;
                            let margin_rate: f64 = commodity_info.bond_wc.try_into().unwrap_or_default();
                            let stock_code = commodity_info.code;

                            if min_value == 0 {
                                if commodity_info.r#type == 1 {
                                    min_value = commodity_info.hands_num
                                } else if commodity_info.r#type == 3 {
                                    if stock_code.len() > 3 && &stock_code.as_str()[0..3] == "688" {
                                        min_value = 200;
                                    } else {
                                        min_value = 100;
                                    }
                                }
                            }

                            if max_value == 0 {
                                if commodity_info.r#type == 1 {
                                    max_value = 2000000;
                                } else if commodity_info.r#type == 3 {
                                    if stock_code.len() > 3 && &stock_code.as_str()[0..3] == "688" {
                                        max_value = 100000;
                                    } else {
                                        max_value = 300000;
                                    }
                                }
                            }

                            if max_single_money == 0_f64 {
                                if commodity_info.r#type == 1 {
                                    max_value = 20000000;
                                } else if commodity_info.r#type == 3 {
                                    max_value = 3000000;
                                }
                            }

                            let stock_info = StockInfo {
                                stock_id: commodity_info.id,
                                stock_code,
                                stock_name: commodity_info.name,
                                trade_state: commodity_info.trade_state as i32,
                                hands_num: commodity_info.hands_num.to_string(),
                                stock_type: commodity_info.stock_type as i32,
                                min_value,
                                max_value,
                                max_single_money,
                                market_id: commodity_info.market_id,
                                market_code: commodity_info.market_code,
                                trade_currency: match &commodity_info.currency as &str {
                                    constdata::CURRENCY_HKD => Currency::Hkd.into(),
                                    constdata::CURRENCY_CNY => Currency::Cny.into(),
                                    constdata::CURRENCY_USD => Currency::Usd.into(),
                                    _ => {
                                        error!("the trade currency is invalid: {}", &commodity_info.currency);
                                        -1
                                    }
                                },
                                margin_rate: match margin_rate.total_cmp(&0_f64) {
                                    Ordering::Equal => 1_f64,
                                    _ => margin_rate,
                                },
                            };

                            vec_stock_info_.write().unwrap().push(stock_info);
                        });
                    }
                });

                vec_stock_info.clone_from(&vec_stock_info_.read().unwrap());

                // vec_stock_info.sort_by(|a, b| a.stock_id.cmp(&b.stock_id));
                //
                // let j = serde_json::to_string_pretty(&vec_stock_info).unwrap();
                //
                // let mut file = std::fs::File::create("data.txt").expect("create failed");
                // file.write_all(&j.into_bytes()).expect("write failed");
                //
                // println!("{}", vec_stock_info.len());
            }
            _ => {
                let key = format!("{}{}", constdata::COMMODITY_KEY, req.stock_id);
                if self.redis_on && self.redis.get_key_is_exists(&key).await {
                    let rmap = self.redis.get_value_by_hgetall(&key).await;
                    vec_stock_info.push(map2stock_info(rmap));
                } else {
                    let sys_commodity = result!(SysCommodity::find_by_id(&self.finances_db, req.stock_id).await);
                    if sys_commodity.is_none() {
                        return Ok(StockInfoResp {
                            ret_code: -1,
                            ret_msg: "SysCommodity数据不存在".to_string(),
                            data: vec![],
                        });
                    }
                    let sys_commodity = sys_commodity.unwrap();

                    let mut stock_info = StockInfo {
                        stock_id: sys_commodity.id,
                        stock_code: sys_commodity.code,
                        stock_name: sys_commodity.name,
                        trade_state: sys_commodity.trade_state as i32,
                        hands_num: sys_commodity.hands_num.to_string(),
                        stock_type: sys_commodity.stock_type as i32,
                        min_value: 0,
                        max_value: 0,
                        max_single_money: 0.0,
                        market_id: sys_commodity.market_id,
                        market_code: "".to_string(),
                        trade_currency: match &sys_commodity.currency as &str {
                            constdata::CURRENCY_HKD => Currency::Hkd.into(),
                            constdata::CURRENCY_CNY => Currency::Cny.into(),
                            constdata::CURRENCY_USD => Currency::Usd.into(),
                            _ => {
                                error!("the trade currency is invalid: {}", &sys_commodity.currency);
                                -1
                            }
                        },
                        margin_rate: 1_f64,
                    };

                    if let Ok(sys_market) = SysMarket::find_by_id(&self.finances_db, sys_commodity.market_id).await {
                        stock_info.market_code = sys_market.market_code.clone();
                    };

                    if let Ok(sys_commodity_ext) = SysCommodityExt::find_by_commodity_id(&self.finances_db, req.stock_id).await {
                        stock_info.max_value = sys_commodity_ext.max_value.try_into().unwrap_or_default();
                        stock_info.min_value = sys_commodity_ext.min_value.try_into().unwrap_or_default();
                        stock_info.max_single_money = sys_commodity_ext.max_money as f64;
                        stock_info.margin_rate = sys_commodity_ext.bond_wc.try_into().unwrap_or_default();
                        if stock_info.margin_rate.eq(&0_f64) {
                            stock_info.margin_rate = 1_f64;
                        }

                        if stock_info.min_value == 0 {
                            if sys_commodity.r#type == 1 {
                                stock_info.min_value = stock_info.hands_num.parse().unwrap_or_default()
                            } else if sys_commodity.r#type == 3 {
                                if stock_info.stock_code.len() > 3 && &stock_info.stock_code.as_str()[0..3] == "688" {
                                    stock_info.min_value = 200;
                                } else {
                                    stock_info.min_value = 100;
                                }
                            }
                        }

                        if stock_info.max_value == 0 {
                            if sys_commodity.r#type == 1 {
                                stock_info.max_value = 2000000;
                            } else if sys_commodity.r#type == 3 {
                                if stock_info.stock_code.len() > 3 && &stock_info.stock_code.as_str()[0..3] == "688" {
                                    stock_info.max_value = 100000;
                                } else {
                                    stock_info.max_value = 300000;
                                }
                            }
                        }

                        if stock_info.max_single_money == 0_f64 {
                            if sys_commodity.r#type == 1 {
                                stock_info.max_value = 20000000;
                            } else if sys_commodity.r#type == 3 {
                                stock_info.max_value = 3000000;
                            }
                        }
                    };

                    vec_stock_info.push(stock_info);
                }
            }
        };

        if !err_flag {
            return Err(Status::not_found("some data cant be found from redis".to_string()));
        }

        // info!("query_stock_info 查询股票基础信息以及交易时间段: {:?}", &vec_stock_info);

        Ok(StockInfoResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_stock_info,
        })
    }

    /// 全部交易对手方账号和特殊账号信息
    pub async fn query_special_account(&self, req: SpecialAccountInfoReq) -> Result<SpecialAccountInfoResp, Status> {
        // info!("query_special_account 查询全部交易对手方账号和特殊账号信息 start");
        // info!("查询全部交易对手方账号和特殊账号信息 输入请求为{:?}", &req);

        let mut accounts = Vec::<SpecialAccount>::new();
        if let Ok(value) = self.redis.get_values_by_lrange(constdata::SPECIAL_ACCOUNT).await {
            if !value.is_empty() {
                for v in value {
                    let vstr: Vec<&str> = v.split("|").collect();
                    let mut spaccount = SpecialAccount { ..Default::default() };
                    if let Ok(account_type) = vstr[0].parse::<i32>() {
                        spaccount.account_type = account_type;
                        if account_type != SpecialAccountType::All as i32 && account_type != req.account_type {
                            continue;
                        }
                    }
                    if let Ok(account) = vstr[1].parse() {
                        spaccount.unit_id = account;
                        accounts.push(spaccount);
                    }
                }
            }
        }

        // info!("query_special_account 查询全部交易对手方账号和特殊账号信息 end");

        Ok(SpecialAccountInfoResp {
            accounts,
            ret_code: 0,
            ret_msg: "".to_owned(),
        })
    }

    /// 市场信息以及交易日信息
    pub async fn query_market_info(&self, req: MarketInfoReq) -> Result<MarketInfoResp, Status> {
        // info!("query_market_info 查询市场信息以及交易日信息 start");
        // info!("查询市场信息以及交易日信息 输入请求为{:?}", &req);

        let mut err_flag = true;
        let mut vec_market_info = Vec::<MarketInfo>::new();

        // 获取当前日期
        let current_date = self.get_current_date();

        let mut map2market_info = |rmap: BTreeMap<String, String>| -> MarketInfo {
            let mut market_info = MarketInfo {
                market_id: 0,
                currency_type: 0,
                market_code: "".to_string(),
                market_type: 0,
                current_date,
                date_type: 0,
            };

            if !rmap.is_empty() {
                if let Some(market_id_str) = rmap.get("id") {
                    market_info.market_id = market_id_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(currency_type) = rmap.get("currency") {
                    market_info.currency_type = get_currency_by_code(&currency_type) as i32;
                } else {
                    err_flag = false;
                }
                if let Some(market_code) = rmap.get("market_code") {
                    market_info.market_code = market_code.clone();
                } else {
                    err_flag = false;
                }
                if let Some(market_type_str) = rmap.get("type") {
                    market_info.market_type = market_type_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
            } else {
                err_flag = false;
            }

            market_info
        };

        match req.market_id {
            0 => {
                let vec_model_data = result!(SysMarket::find_all(&self.finances_db).await);

                for model_datum in vec_model_data {
                    let mut market_info = MarketInfo {
                        market_id: model_datum.id,
                        currency_type: match model_datum.currency {
                            CurrencyType::HKD => get_currency_by_code(&constdata::CURRENCY_HKD.to_string()) as i32,
                            CurrencyType::CNY => get_currency_by_code(&constdata::CURRENCY_CNY.to_string()) as i32,
                            CurrencyType::USD => get_currency_by_code(&constdata::CURRENCY_USD.to_string()) as i32,
                        },
                        market_code: model_datum.market_code.clone(),
                        market_type: model_datum.r#type as i64,
                        current_date,
                        date_type: 0,
                    };

                    let trading_day_type = self.get_date_type(market_info.market_id, current_date).await?;
                    market_info.date_type = match trading_day_type {
                        TradingDayType::NonTradingDay => TradingDayType::NonTradingDay as i32,
                        TradingDayType::TradingDay => TradingDayType::TradingDay as i32,
                        TradingDayType::HalfTradingDay => TradingDayType::HalfTradingDay as i32,
                    };

                    vec_market_info.push(market_info);
                }
            }
            _ => {
                let key = format!("{}{}", constdata::MARKET_KEY, req.market_id);

                let trading_day_type = self.get_date_type(req.market_id, current_date).await?;
                let date_type = match trading_day_type {
                    TradingDayType::NonTradingDay => TradingDayType::NonTradingDay as i32,
                    TradingDayType::TradingDay => TradingDayType::TradingDay as i32,
                    TradingDayType::HalfTradingDay => TradingDayType::HalfTradingDay as i32,
                };

                let market_info = if self.redis_on && self.redis.get_key_is_exists(&key).await {
                    let rmap = self.redis.get_value_by_hgetall(&key).await;
                    let mut market_info = map2market_info(rmap);
                    market_info.date_type = date_type;
                    market_info
                } else {
                    let model_data = result!(SysMarket::find_by_id(&self.finances_db, req.market_id).await);
                    if self.redis_on {
                        self.write_redis_hash(&key, &model_data).await?;
                    }

                    MarketInfo {
                        market_id: model_data.id,
                        currency_type: match model_data.currency {
                            CurrencyType::HKD => get_currency_by_code(&constdata::CURRENCY_HKD.to_string()) as i32,
                            CurrencyType::CNY => get_currency_by_code(&constdata::CURRENCY_CNY.to_string()) as i32,
                            CurrencyType::USD => get_currency_by_code(&constdata::CURRENCY_USD.to_string()) as i32,
                        },
                        market_code: model_data.market_code.clone(),
                        market_type: model_data.r#type as i64,
                        current_date,
                        date_type,
                    }
                };
                vec_market_info.push(market_info);
            }
        };

        if !err_flag {
            return Err(Status::not_found("query_market_info: some data cant be found from redis".to_string()));
        }

        // info!("query_market_info 查询市场信息以及交易日信息 end");

        Ok(MarketInfoResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_market_info,
        })
    }

    /// 临时休市信息
    pub async fn query_market_close_info(&self, req: MarketCloseInfoReq) -> Result<MarketCloseInfoResp, Status> {
        debug!("query_market_close_info 查询临时休市信息 :{:?}", &req);

        let mut vec_market_close_info = Vec::<MarketCloseInfo>::new();
        if let Ok(value) = self.redis.get_values_by_lrange(constdata::MARKET_CLOSE_TIME).await {
            if !value.is_empty() {
                for v in value {
                    let mut market_close_info = MarketCloseInfo {
                        market_id: 0,
                        start_time: "".to_string(),
                        end_time: "".to_string(),
                        close_type: 0,
                    };
                    let vstr: Vec<&str> = v.split("|").collect();
                    market_close_info.market_id = vstr[1].parse().unwrap_or_default();
                    market_close_info.start_time = vstr[2].parse().unwrap_or_default();
                    market_close_info.end_time = vstr[3].parse().unwrap_or_default();
                    market_close_info.close_type = vstr[4].parse().unwrap_or_default();

                    vec_market_close_info.push(market_close_info);
                }
            }
        }

        // info!("query_market_close_info 查询临时休市信息 end");

        Ok(MarketCloseInfoResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_market_close_info,
        })
    }

    /// 汇率查询(基准币为港币)
    pub async fn query_exchange_rate(&self, req: ExchangeRateReq) -> Result<ExchangeRateResp, Status> {
        // info!("query_exchange_rate 查询汇率 start");
        // info!("query_exchange_rate 查询汇率 输入请求为{:?}", &req);

        // if req.currency >= constdata::CURRENCY_NUM {
        //     return Err(Status::invalid_argument(format!("该币种不存在: {}", req.currency)))
        // }

        let currency = Currency::try_from(req.currency).unwrap_or_default().as_str_name().to_uppercase();
        let base_currency = Currency::try_from(req.base_currency).unwrap_or_default().as_str_name().to_uppercase();

        if currency == "UNDEF" || base_currency == "UNDEF" {
            return Ok(ExchangeRateResp {
                ret_code: 101,
                ret_msg: format!("币种有误: currency: {}, base_currency: {}", req.currency, req.base_currency),
                data: None,
            });
        } else if currency == base_currency {
            return Ok(ExchangeRateResp {
                ret_code: 0,
                ret_msg: "".to_owned(),
                data: Some(ExchangeRate {
                    currency: req.currency,
                    base_currency: req.base_currency,
                    buy_rate: 1.0,
                    sell_rate: 1.0,
                    modify_time: Local::now().timestamp(),
                }),
            });
        }

        let (sell_key, buy_key) = (format!("RATE_{}_{}_STOCK_SELL", &currency, &base_currency), format!("RATE_{}_{}_STOCK_BUY", &currency, &base_currency));

        let (sell_rate, modify_time) = self.get_rate(&sell_key).await?;
        let (buy_rate, _) = self.get_rate(&buy_key).await?;

        // info!("query_exchange_rate 查询汇率 end");
        Ok(ExchangeRateResp {
            ret_code: 0,
            ret_msg: "".to_owned(),
            data: Some(ExchangeRate {
                currency: req.currency,
                base_currency: req.base_currency,
                buy_rate,
                sell_rate,
                modify_time,
            }),
        })
    }

    /// 账户信息查询
    pub async fn query_account_info(&self, req: AccountInfoReq) -> Result<AccountInfoResp, Status> {
        // info!("query_account_info 账户信息查询 start");
        // info!("query_account_info 输入请求为{:?}", &req);

        let mut err_flag = true;
        let mut vec_account_info = Vec::<AccountInfo>::new();

        let current_date_timestamp = Local::now().timestamp();

        let mut map2account_info = |rmap: BTreeMap<String, String>| -> AccountInfo {
            info!("get account info from map: {:?}", rmap);
            let mut account_info = AccountInfo {
                user_id: 0,
                trade_mode: 0,
                agent_account: 0,
                level_rate: "".to_string(),
                expire_date: 0,
                warning_line: 0.0,
                close_line: 0.0,
                trade_state: 0,
                gem_limit: 0.0,
                rq_account_type: 0,
            };

            if !rmap.is_empty() {
                if let Some(unit_id_str) = rmap.get("user_id") {
                    account_info.user_id = unit_id_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(trade_mode_str) = rmap.get("trade_mode") {
                    account_info.trade_mode = trade_mode_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(agent_account_str) = rmap.get("agent_account") {
                    account_info.agent_account = agent_account_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(level_rate) = rmap.get("level_rate") {
                    account_info.level_rate = level_rate.clone();
                } else {
                    err_flag = false;
                }
                if let Some(expire_date_str) = rmap.get("expire_date") {
                    account_info.expire_date = expire_date_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(warning_line_str) = rmap.get("warning_line") {
                    account_info.warning_line = warning_line_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(close_line_str) = rmap.get("close_line") {
                    account_info.close_line = close_line_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(trade_state_str) = rmap.get("trade_state") {
                    account_info.trade_state = trade_state_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                if let Some(gem_limit_str) = rmap.get("gem_limit") {
                    account_info.gem_limit = gem_limit_str.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
                //rq_state
                if let Some(rq_state) = rmap.get("rq_account_type") {
                    account_info.rq_account_type = rq_state.parse().unwrap_or_default();
                } else {
                    err_flag = false;
                }
            } else {
                err_flag = false;
            }

            account_info
        };
        // info!("req.user_id: {}", req.user_id);
        match req.user_id {
            0 => {
                let future_users_trade_account = UsersTradeAccount::find_account_all(&self.customer_db); //获取全部交易id
                let future_user_level_record = UsersLevelRecord::find_by_expire_date(&self.finances_db, current_date_timestamp); //获取全部有效账户信息
                let future_user_chinext_rate = UsersChinextRate::find_all(&self.finances_db); //获取全部创业板限制
                let (re_users_trade_account, re_user_level_record, re_user_chinext_rate): (Result<Vec<UsersTradeAccount>>, Result<Vec<UsersLevelRecord>>, Result<Vec<UsersChinextRate>>) =
                    join!(future_users_trade_account, future_user_level_record, future_user_chinext_rate);
                let (vec_users_trade_account, vec_user_level_record, vec_user_chinext_rate) = result!(re_users_trade_account, re_user_level_record, re_user_chinext_rate);

                for users_trade_account in vec_users_trade_account {
                    let user_level_record = vec_user_level_record.iter().find(|&user_level_record| users_trade_account.user_id == user_level_record.user_id as i64);

                    let mut account_info = AccountInfo {
                        user_id: 0,
                        trade_mode: 1,
                        agent_account: 0,
                        level_rate: "".to_string(),
                        expire_date: 0,
                        warning_line: 0.0,
                        close_line: 0.0,
                        trade_state: 0,
                        gem_limit: 0.25,
                        rq_account_type: 0,
                    };

                    if let Some(user_level_record) = user_level_record {
                        //获取对应有效账户信息
                        account_info.user_id = user_level_record.user_id;
                        account_info.level_rate = user_level_record.lever_rate.to_string();
                        account_info.expire_date = user_level_record.expire_date;
                        account_info.warning_line = user_level_record.warn_line.try_into().unwrap_or_default();
                        account_info.close_line = user_level_record.close_line.try_into().unwrap_or_default();
                        account_info.trade_state = users_trade_account.status as i64;
                        account_info.rq_account_type = users_trade_account.rq_state as i32;
                    } else {
                        //获取对应有效账户信息
                        account_info.user_id = users_trade_account.user_id;
                        account_info.level_rate = "0.0".to_string();
                        account_info.expire_date = 0;
                        account_info.warning_line = 0.0;
                        account_info.close_line = 0.0;
                        account_info.trade_state = users_trade_account.status as i64;
                        account_info.rq_account_type = users_trade_account.rq_state as i32;
                    }

                    let user_chinext_rate = vec_user_chinext_rate.iter().find(|&user_chinext_rate| users_trade_account.user_id == user_chinext_rate.user_id as i64);

                    if let Some(user_chinext_rate) = user_chinext_rate {
                        //获取对应有效账户创业板限制
                        account_info.gem_limit = user_chinext_rate.rate.try_into().unwrap_or(0.25);
                    }

                    vec_account_info.push(account_info);
                }
            }
            _ => {
                let key = format!("{}{}", constdata::USER_KEY, req.user_id);
                // info!("key: {}", key);
                // info!("get_key_is_exists: {:?}", self.redis.get_key_is_exists(&key).await);
                let account_info = if self.redis_on && self.redis.get_key_is_exists(&key).await && {
                    if let Ok(vec_key) = self.redis.get_keys_from_hash(&key).await {
                        // info!("vec_key: {:?}", &vec_key);
                        vec_key.contains(&"user_id".to_string())
                            && vec_key.contains(&"trade_mode".to_string())
                            && vec_key.contains(&"agent_account".to_string())
                            && vec_key.contains(&"level_rate".to_string())
                            && vec_key.contains(&"expire_date".to_string())
                            && vec_key.contains(&"warning_line".to_string())
                            && vec_key.contains(&"close_line".to_string())
                            && vec_key.contains(&"trade_state".to_string())
                            && vec_key.contains(&"gem_limit".to_string())
                        // && vec_key.contains(&"rq_account_type".to_string())
                    } else {
                        false
                    }
                } {
                    let rmap = self.redis.get_value_by_hgetall(&key).await;
                    // info!("get_value_by_hgetall: {:?}", rmap);
                    map2account_info(rmap)
                } else {
                    // info!("start to query from database......: {}", req.user_id);
                    let re_users_trade_account = UsersTradeAccount::find_account_by_user_id(&self.customer_db, req.user_id).await;
                    let re_user_level_record = UsersLevelRecord::find_by_user_id(&self.finances_db, req.user_id, current_date_timestamp).await; //查找账户信息
                    let re_user_chinext_rate = UsersChinextRate::find_by_user_id(&self.finances_db, req.user_id).await;
                    let re_user_agent_trading = UsersAgentTrading::find_by_user_id(&self.customer_db, req.user_id).await;
                    let (users_trade_account, user_level_record, user_chinext_rate, user_agent_trading) = result!(re_users_trade_account, re_user_level_record, re_user_chinext_rate, re_user_agent_trading);

                    let mut account_info = if user_level_record.is_none() {
                        AccountInfo {
                            user_id: req.user_id,
                            trade_mode: if user_agent_trading.is_none() { 1 } else { 2 },
                            agent_account: if user_agent_trading.is_some() { user_agent_trading.unwrap().agent_id.into() } else { 0 },
                            level_rate: "0.0".to_string(),
                            expire_date: 0,
                            warning_line: 0.0,
                            close_line: 0.0,
                            trade_state: users_trade_account.status as i64,
                            gem_limit: 0.25,
                            rq_account_type: users_trade_account.rq_account_type as i32,
                        }
                    } else {
                        let user_level_record = user_level_record.unwrap();
                        AccountInfo {
                            user_id: user_level_record.user_id,
                            trade_mode: if user_agent_trading.is_none() { 1 } else { 2 },
                            agent_account: if user_agent_trading.is_some() { user_agent_trading.unwrap().agent_id.into() } else { 0 },
                            level_rate: user_level_record.lever_rate.to_string(),
                            expire_date: user_level_record.expire_date,
                            warning_line: user_level_record.warn_line.try_into().unwrap_or_default(),
                            close_line: user_level_record.close_line.try_into().unwrap_or_default(),
                            trade_state: users_trade_account.status as i64,
                            gem_limit: 0.25,
                            rq_account_type: users_trade_account.rq_account_type as i32,
                        }
                    };

                    if user_chinext_rate.is_some() {
                        //查找创业板限制
                        account_info.gem_limit = user_chinext_rate.unwrap().rate.try_into().unwrap_or(0.25);
                    }

                    if self.redis_on {
                        //写入redis
                        self.write_redis_hash(&key, &account_info).await?;
                    }

                    account_info
                };

                vec_account_info.push(account_info);
            }
        };

        if !err_flag {
            return Err(Status::not_found("some data cant be found from redis".to_string()));
        }

        // info!("query_account_info 账户信息查询 end:{:?}", &vec_account_info);

        Ok(AccountInfoResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_account_info,
        })
    }

    /// 交易日期查询
    pub async fn query_trade_date(&self, req: TradeDateReq) -> Result<TradeDateResp, Status> {
        // info!("query_trade_date 交易日期查询 start");
        // info!("query_trade_date,输入请求为{:?}", &req);

        let mut vec_trade_date = Vec::<TradeDateInfo>::new();

        // 获取当前日期
        let current_date = match req.query_type {
            0 => self.get_current_date(),
            _ => result!(PhoenixSysSystem::find_init_date(&self.stock_db).await).into(),
        };

        let query_date = match req.query_date {
            0 => current_date,
            _ => req.query_date.into(),
        };

        if req.date_offset == 0 {
            return Ok(TradeDateResp {
                ret_code: 0,
                ret_msg: "success".to_string(),
                data: vec![TradeDateInfo {
                    market_id: req.market_id,
                    current_date: current_date as i32,
                    target_date: current_date as i32,
                    date_type: req.query_type,
                }],
            });
        }

        let keys = match req.market_id {
            0 => vec![
                format!("{}{}", constdata::HOLIDAY_MARKET_KEY, 101),
                format!("{}{}", constdata::HOLIDAY_MARKET_KEY, 102),
                format!("{}{}", constdata::HOLIDAY_MARKET_KEY, 103),
            ],
            _ => vec![format!("{}{}", constdata::HOLIDAY_MARKET_KEY, req.market_id)],
        };

        for key in keys {
            if let Ok(date_list) = self.redis.get_values_by_lrange(&key).await {
                if date_list.is_empty() {
                    error!("没有找到任何日期信息 {}", &key);
                    return Err(Status::not_found(format!("没有找到任何日期信息 {}", &key)));
                }

                let vec_date_info: Vec<(i64, i64)> = date_list
                    .iter()
                    .filter_map(|date_info| {
                        let vstr: Vec<&str> = date_info.split(",").collect();
                        if vstr.len() == 2 {
                            if let (Ok(date), Ok(date_type)) = (vstr[0].parse::<i64>(), vstr[1].parse::<i64>()) {
                                match req.query_type.try_into() {
                                    Ok(QueryType::Common) => return Some((date, date_type)),
                                    Ok(QueryType::TradingDay) => {
                                        // 查询交易日 date_type 0和1均为交易日
                                        // if date_type == QueryType::TradingDay as i64 {
                                        return Some((date, date_type));
                                        // }
                                    }
                                    Ok(QueryType::SettleDay) => {
                                        // 交收日只取date_type为TradingDay的日期, 半日市不交收
                                        if date_type == TradingDayType::TradingDay as i64 {
                                            return Some((date, date_type));
                                        }
                                    }
                                    Err(_) => {}
                                }
                            }
                        }

                        None
                    })
                    .collect();

                if vec_date_info.is_empty() {
                    error!("提取日期信息为空");
                    return Err(Status::unknown("提取日期信息为空"));
                }

                let ret_date_info_index = vec_date_info.iter().position(|&date_info| date_info.0 >= query_date);

                let index = if let Some(index) = ret_date_info_index {
                    index
                } else {
                    error!("没有找到任何交易日");
                    return Err(Status::not_found("没有找到任何交易日"));
                };

                let (mut target_date, mut date_type) = (0, 0);

                match req.query_type.try_into() {
                    Ok(QueryType::Common) => {
                        if let Ok(date) = NaiveDate::parse_from_str(&format!("{}", query_date), "%Y%m%d") {
                            let offset_date = date + Duration::days(req.date_offset.into());
                            if let Ok(offset_date) = offset_date.format("%Y%m%d").to_string().parse::<i64>() {
                                if let Some(ret) = vec_date_info.iter().find(|&&date_info| date_info.0 == offset_date) {
                                    (target_date, date_type) = *ret;
                                } else {
                                    (target_date, date_type) = (offset_date, TradingDayType::NonTradingDay as i64);
                                }
                            }
                        }
                    }
                    Ok(_) => {
                        if vec_date_info[index].0 == query_date {
                            if (index + req.date_offset as usize) >= vec_date_info.len() {
                                error!("偏移日期超出范围");
                                return Err(Status::unknown("偏移日期超出范围"));
                            }
                            (target_date, date_type) = vec_date_info[index + req.date_offset as usize];
                        } else if vec_date_info[index].0 > query_date {
                            if (index as i32 + req.date_offset - 1) >= vec_date_info.len() as i32 || (index as i32 + req.date_offset - 1) < 0 {
                                error!("偏移日期超出范围: {}, {}, {:?}", vec_date_info[index].0, query_date, &req);
                                return Err(Status::unknown("偏移日期超出范围"));
                            }
                            (target_date, date_type) = vec_date_info[index + req.date_offset as usize - 1];
                        }
                    }
                    Err(_) => {
                        error!("交易日查询类型错误: {}", req.query_type);
                        return Err(Status::unknown(format!("交易日查询类型错误: {}", req.query_type)));
                    }
                }

                vec_trade_date.push(TradeDateInfo {
                    market_id: {
                        let vstr: Vec<&str> = key.split("_").collect();
                        vstr[2].parse::<i64>().unwrap_or_default()
                    },
                    current_date: current_date.try_into().unwrap(),
                    target_date: target_date.try_into().unwrap(),
                    date_type: date_type as i32,
                })
            }
        }

        // info!("query_trade_date 交易日期查询 end");

        Ok(TradeDateResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            data: vec_trade_date,
        })
    }

    ///查询用户品种的保证金
    pub async fn query_unit_stock_margin(&self, req: UserStockMarginReq) -> Result<UserStockMarginResp, Status> {
        // info!("query_unit_stock_margin 查询用户品种的保证金 start");
        // info!("query_unit_stock_margin,输入请求为{:?}", &req);

        let key = format!("{}{}_{}", constdata::BZJ_KEY, req.user_id, req.stock_id);

        let rate: f64 = if self.redis_on && self.redis.get_key_is_exists(&key).await {
            let bond_wc_str = self.redis.get_value_by_get(&key).await;
            result!(bond_wc_str.parse::<f64>())
        } else {
            let sys_commodity_channel: Option<SysCommodityChannel> = result!(SysCommodityChannel::find_by_user_id_and_commodity_id(&self.finances_db, req.user_id, req.stock_id).await);
            if let Some(sys_commodity_channel) = sys_commodity_channel {
                let rate: f64 = sys_commodity_channel.rate.try_into().unwrap_or_default();
                if self.redis_on {
                    result!(self.redis.set_str_value(&key, constdata::EXPARE_TIME_8_HOUR, &rate.to_string()).await);
                }
                rate
            } else {
                0_f64
            }
        };

        // info!("query_unit_stock_margin 查询用户品种的保证金 end");

        Ok(UserStockMarginResp {
            ret_code: 0,
            ret_msg: "success".to_string(),
            margin_rate: rate,
        })
    }

    /// 查询费用设置
    pub async fn query_fee_setting(&self, req: FeeSettingReq) -> Result<FeeSettingResp, Status> {
        // info!("query_fee_setting 查询费用设置 start");
        // info!("query_fee_setting,输入请求为{:?}", &req);

        let mut vec_fee_setting = Vec::<FeeSetting>::new();

        match &req.fee_type as &str {
            "" => {
                for fee_type in constdata::FEE_TYPE1 {
                    let fee_setting = self.fee_setting_cache.get_fee(fee_type, &req, &self.stock_db).await?;
                    if fee_setting.is_some() {
                        vec_fee_setting.push(fee_setting.unwrap());
                    }
                }
            }
            _ => {
                let fee_setting = self.fee_setting_cache.get_fee(&req.fee_type, &req, &self.stock_db).await?;
                if fee_setting.is_some() {
                    vec_fee_setting.push(fee_setting.unwrap());
                }
            }
        }

        // info!("query_fee_setting 查询费用设置 end");

        Ok(FeeSettingResp {
            fee_settings: vec_fee_setting,
            ret_code: 0,
            ret_msg: "".to_owned(),
        })
    }

    /// 查询通道保证金设置
    pub async fn query_stock_channel_margin(&self, req: StockMarginReq) -> Result<StockMarginResp, Status> {
        // info!("query_stock_channel_margin 查询通道保证金比例 start");
        // info!("query_stock_channel_margin,输入请求为{:?}", &req);

        if req.stock_id <= 0 || req.channel_id <= 0 {
            return Ok(StockMarginResp {
                ret_code: 102,
                ret_msg: format!("请求参数有误: {}{}", req.stock_id, req.channel_id),
                stock_margin: None,
            });
        }

        // 通道保证金设置: key  channel_margin_{stock_id}_{channel_id}
        let key = format!("{}{}_{}", constdata::CHANNEL_MARGIN_KEY, req.stock_id, req.channel_id);

        let rate: f64 = if self.redis_on && self.redis.get_key_is_exists(&key).await {
            let bond_wc_str = self.redis.get_value_by_get(&key).await;
            result!(bond_wc_str.parse::<f64>())
        } else {
            let sys_commodity_channel = result!(SysCommodityChannel::find_by_commodity_id_and_channel(&self.finances_db, req.stock_id, req.channel_id).await);
            let rate: f64 = match sys_commodity_channel {
                Some(sys_commodity_channel) => sys_commodity_channel.rate.try_into().unwrap_or_default(),
                None => self.settings.system.default_margin_rate,
            };

            if self.redis_on {
                result!(self.redis.set_str_value(&key, constdata::EXPARE_TIME_8_HOUR, &rate.to_string()).await);
            }
            rate
        };

        // info!("query_stock_channel_margin 查询通道保证金比例 end");

        Ok(StockMarginResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            stock_margin: Some(StockMargin {
                stock_id: req.stock_id,
                channel_id: req.channel_id,
                margin_rate: rate,
            }),
        })
    }

    /// 查询停牌股票信息
    pub async fn query_stock_suspension_info(&self, _req: StockSuspensionReq) -> Result<StockSuspensionResp, Status> {
        // info!("query_stock_suspension_info 查询停牌股票信息 :{:?}", &req);

        let mut stock_suspension = Vec::<StockSuspension>::new();

        let ve_data: Vec<StockSuspensionRecordSelectRes> = result!(SysStockSuspensionRecord::find_all(&self.finances_db).await);

        for datum in ve_data {
            let data = StockSuspension {
                stock_id: datum.stock_id as i64,
                susp_date: datum.susp_date,
                process_date: datum.process_date as i32,
                margin_rate_increment: datum.margin_rate_increment.try_into().unwrap_or_default(),
            };

            stock_suspension.push(data);
        }

        // info!("query_stock_suspension_info 查询停牌股票信息 end");

        Ok(StockSuspensionResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            stock_suspension,
        })
    }

    /// 股票交易时间查询
    pub async fn query_stock_trade_time(&self, req: StockTradeTimeReq) -> Result<StockTradeTimeResp, Status> {
        // info!("query_stock_trade_time 股票交易时间查询 start");
        // info!("query_stock_trade_time,输入请求为{:?}", &req);

        let condition = match req.stock_id {
            0 => Condition::all()
                .add(sys_trade_date_config::Column::MarketId.eq(req.exchange_id))
                .add(sys_trade_date_config::Column::CommodityId.eq(0)),
            _ => Condition::all().add(sys_trade_date_config::Column::CommodityId.eq(req.stock_id)),
        };
        let vec_sys_trade_date: Vec<SysTradeDateConfig> = result!(SysTradeDateConfig::find_by_condition(&self.finances_db, condition).await);
        if vec_sys_trade_date.is_empty() {
            return match req.stock_id {
                0 => Err(Status::not_found(format!("交易时间不存在 stock_id: {} exchange_id: {}", req.stock_id, req.exchange_id))),
                _ => Ok(StockTradeTimeResp {
                    ret_code: 0,
                    ret_msg: "交易时间为空".to_string(),
                    tradetimes: vec![],
                }),
            };
        }

        let trade_times = vec_sys_trade_date
            .iter()
            .map(|sys_trade_date| {
                let mut times = String::new();
                if !(sys_trade_date.open_time1.is_empty() || sys_trade_date.close_time1.is_empty()) {
                    times = sys_trade_date.open_time1.clone() + "|" + sys_trade_date.close_time1.as_str();
                }
                if !(sys_trade_date.open_time2.is_empty() || sys_trade_date.close_time2.is_empty()) {
                    times = times.clone() + "," + sys_trade_date.open_time2.as_str() + "|" + sys_trade_date.close_time2.as_str();
                }
                if !(sys_trade_date.open_time3.is_empty() || sys_trade_date.close_time3.is_empty()) {
                    times = times.clone() + "," + sys_trade_date.open_time3.as_str() + "|" + sys_trade_date.close_time3.as_str();
                }
                if !(sys_trade_date.open_time4.is_empty() || sys_trade_date.close_time4.is_empty()) {
                    times = times.clone() + "," + sys_trade_date.open_time4.as_str() + "|" + sys_trade_date.close_time4.as_str();
                }
                if !(sys_trade_date.open_time5.is_empty() || sys_trade_date.close_time5.is_empty()) {
                    times = times.clone() + "," + sys_trade_date.open_time5.as_str() + "|" + sys_trade_date.close_time5.as_str();
                }

                StockTradeTime {
                    times,
                    op_type: sys_trade_date.open_type as i32,
                }
            })
            .collect::<Vec<StockTradeTime>>();

        // info!("query_stock_trade_time 股票交易时间查询 end");

        Ok(StockTradeTimeResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            tradetimes: trade_times,
        })
    }

    /// 融券额度查询
    pub async fn query_securities_borrow_limit(&self, req: SecuritiesBorrowLimitReq) -> Result<SecuritiesBorrowLimitResp, Status> {
        // info!("query_securities_borrow_limit 融券额度查询 start");
        // info!("query_securities_borrow_limit,输入请求为{:?}", &req);

        let rq_infos = result!(UsersRq::find_by_date(&self.finances_db, req.stock_id).await);
        info!("query_securities_borrow_limit users rq data:{:?}", &rq_infos);
        let mut map = HashMap::new();

        rq_infos.clone().into_iter().unique_by(|info| info.commodity_id).for_each(|info| {
            map.insert(info.commodity_id, Vec::<UserSecuritiesBorrowLimit>::new());
        });

        rq_infos.iter().for_each(|info| {
            let vec_info = map.get_mut(&info.commodity_id);
            if vec_info.is_some() {
                vec_info.unwrap().push(UserSecuritiesBorrowLimit {
                    user_id: info.user_id,
                    available_amount: info.credit_num as i32,
                    used_amount: info.use_num,
                })
            }
        });

        let securities_borrow_limit: Vec<_> = map
            .into_iter()
            .map(|(stock_id, vec_infos)| SecuritiesBorrowLimit {
                stock_id,
                user_securities_borrow_limit: vec_infos,
            })
            .collect();

        // info!("query_securities_borrow_limit 融券额度查询 end:{:?}", &securities_borrow_limit);

        Ok(SecuritiesBorrowLimitResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            securities_borrow_limit,
        })
    }

    /// 融券优先级查询
    pub async fn query_securities_borrow_level(&self, req: SecuritiesBorrowLevelReq) -> Result<SecuritiesBorrowLevelResp, Status> {
        // info!("query_securities_borrow_level 融券优先级查询 start");
        // info!("query_securities_borrow_level,输入请求为{:?}", &req);

        let mut data = vec![];

        let res_str: Vec<_> = req.user_ids.split(";").collect();
        for user_id in res_str {
            let user_id = result!(user_id.parse::<i64>());
            let users_trade_account = result!(UsersTradeAccount::find_account_by_user_id(&self.customer_db, user_id).await);
            if users_trade_account.rq_level == 0 {
                continue;
            }
            data.push(SecuritiesBorrowLevel {
                user_id: users_trade_account.user_id,
                level: users_trade_account.rq_level,
                status: users_trade_account.rq_state as i32,
            });
        }

        let data: Vec<_> = data.into_iter().sorted_by(|a, b| Ord::cmp(&a.level, &b.level)).collect();

        // info!("query_securities_borrow_level 融券优先级查询 end");

        Ok(SecuritiesBorrowLevelResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            securities_borrow_level: data,
        })
    }

    /// 查询股票通道限额
    pub async fn query_stock_channel_quota(&self, req: StockQuotaReq) -> Result<StockQuotaResp, Status> {
        // info!("query_stock_channel_quota 融券优先级查询 start");
        // info!("query_stock_channel_quota,输入请求为{:?}", &req);

        if req.stock_id <= 0 || req.user_id <= 0 {
            return Err(Status::invalid_argument(format!("请求参数有误: {} {}", req.user_id, req.stock_id)));
        }

        let sys_commodity_channel = result!(SysCommodityChannel::find_by_user_id_and_commodity_id_(&self.finances_db, req.user_id, req.stock_id).await);
        let stock_quota = match sys_commodity_channel {
            Some(sys_commodity_channel) => {
                if !sys_commodity_channel.min_hold.is_empty() && !sys_commodity_channel.max_hold.is_empty() {
                    Some(StockQuota {
                        min_hold: sys_commodity_channel.min_hold.parse::<i32>().unwrap_or_default(),
                        max_hold: sys_commodity_channel.max_hold.parse::<i32>().unwrap_or_default(),
                    })
                } else {
                    None
                }
            }
            None => None,
        };

        // info!("query_stock_channel_quota 查询股票通道限额 end");

        Ok(StockQuotaResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            stock_quota,
        })
    }

    pub async fn query_market_code(&self, req: MarketCodeReq) -> Result<MarketCodeResp, Status> {
        // info!("query_market_code 查询市场代码 start");
        info!("query_market_code 输入请求为{:?}", &req);
        let market_info = SysCommodity::query_market_code(&self.finances_db, req.stock_code).await;
        if market_info.is_err() {
            error!("SysCommodity query_market_code fail: {}", market_info.as_ref().unwrap_err());
            return Err(Status::unknown("SysCommodity query_market_code fail"));
        }
        let market_info = market_info.unwrap();
        if market_info.is_none() {
            error!("not found the market_info");
            return Err(Status::not_found("not found the market_info"));
        }

        let market_info = market_info.unwrap();

        Ok(MarketCodeResp {
            ret_code: 0,
            ret_msg: "".to_string(),
            market_code: market_info.market_code,
            exchange_id: market_info.id as i32,
        })
    }
}
