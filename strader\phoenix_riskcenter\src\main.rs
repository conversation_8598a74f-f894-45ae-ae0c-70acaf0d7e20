// use crate::server::ServerHandler;
mod basicdata;
mod client;
mod config;
mod controller;
// mod protofiles;
mod server;
mod service;
#[cfg(test)]
mod test;
use crate::config::settings::Settings;
use anyhow::Result;
use common::logclient::*;
use protoes::phoenixriskcenter::phoenix_riskcenter_server::PhoenixRiskcenterServer;
use server::ServerHandler;
use tracing::*;
// use utility::loggings;

// #[tokio::main]
// #[tokio::main(flavor = "multi_thread")]
#[tokio::main(flavor = "multi_thread", worker_threads = 4)]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/riskcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_riskcenter";
    let dir = "./log";

    let settings = Settings::new().expect("read config error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;
    // init_logclient(&settings.servers.logcenterserver, "phoenix_riskcenter").await;

    let server = prepare(&settings).await.expect("Init server error......");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    // let grpc_stub = create_controller(settings).await;

    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    // let addr = "0.0.0.0:60000".parse().unwrap();
    let addr_url = format!("{}:{}", settings.application.apphost, settings.application.appport);
    let addr = addr_url.as_str().parse().unwrap();
    // info!("Starting phoenix riskcenter service on {}", addr);
    // info!(
    //     "Starting phoenix riskcenter service on {},name: {} version: {} description: {}",
    //     addr,
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // );

    // log_debug(format!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION")).as_str()).await;
    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .add_service(PhoenixRiskcenterServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    // log_debug("Shutted down, wait for final clear").await;
    on_leave.leave().await;
    log_debug("phoenix_riskcenter shutted down").await;
    Ok(())
}
