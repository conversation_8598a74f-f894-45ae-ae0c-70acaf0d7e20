use anyhow::{anyhow, Result};
use std::{collections::HashMap, sync::Arc};
use tracing::*;

use protoes::hqmsg::YsHqInfo;
use protoes::phoenixtickcenter::{LastPriceReq, Last<PERSON>riceResp, TickReq, TickResp};
use utility::timeutil;

use super::service::prelude::*;
use super::service::{errors, persistservice::PersistService};
use crate::client::{cassandraclient::CassandraClient, sledclient::SledClient, tickmqclient::TickMqClient};
use crate::commonutil::commonutil::CommonUtil;

#[derive(Clone)]
pub struct TickCenterController {
    pub tick_ctl: Arc<TickService>,
    pub common_util: Arc<CommonUtil>,
    pub cassandra_client: Option<Arc<CassandraClient>>,
    pub tick_mq: Option<Arc<TickMqClient>>,
    pub sledclient: Arc<SledClient>,
    // pub ticks_cache: Arc<RwLock<Vec<YsHqInfo>>>,
}

impl TickCenterController {
    pub async fn send_tick_to_mq(&self, tick: &YsHqInfo) -> Result<()> {
        if let Some(tick_mq) = &self.tick_mq {
            let market_type = PersistService::get_markettype(&tick.exchange_id).await;
            if let Err(err) = tick_mq.publish(market_type, tick).await {
                error!("push tick data to mq err: {:?}", err);
                return Err(anyhow!("push tick data to mq err: {:?}", err));
            }
        }

        Ok(())
    }

    pub async fn persist_ticks(&self) -> Result<()> {
        let mut ticks = self.tick_ctl.cache.write().await;
        if ticks.is_empty() {
            return Ok(()); // 如果没有tick数据，直接返回
        }
        // let ticks_clone = ticks.clone();
        // ticks.clear();
        //替换clone 和 clear std::mem::take 会将 ticks 置空并返回原有内容，避免 clone
        let ticks_clone = std::mem::take(&mut *ticks);
        drop(ticks);

        if let Err(err) = self.insert_local_db(&ticks_clone).await {
            error!("{:?}", err);
            return Err(anyhow!(err));
        }

        if let Some(cassandra_client) = &self.cassandra_client {
            let now = std::time::Instant::now();
            PersistService::insert_ticks_into_cassandra(&ticks_clone, cassandra_client).await?;
            info!("Tick数据写入Cassandra完成,数据量:{},用时:{:?}", ticks_clone.len(), now.elapsed());
        }

        Ok(())
    }

    pub async fn insert_local_db(&self, ticks: &Vec<YsHqInfo>) -> Result<()> {
        // let mut tick_map: HashMap<String, YsHqInfo> = HashMap::new();
        // for val in ticks.iter() {
        //     let current_time = timeutil::convert_datetime_to_timestamp(&val.tapidtstamp);
        //     if let Some(existing_tick) = tick_map.get(&val.contract_no1) {
        //         let exist_time = timeutil::convert_datetime_to_timestamp(&existing_tick.tapidtstamp);
        //         if current_time < exist_time {
        //             continue; // 如果当前时间小于已存在时间，则跳过
        //         }
        //     }
        //     tick_map.insert(val.contract_no1.clone(), val.clone());
        //     // let info = tick_map.get(&val.contract_no1);
        //     // if info.is_none() {
        //     //     tick_map.insert(val.contract_no1.clone(), val.clone());
        //     //     continue;
        //     // }
        //     // let exist_tick = info.unwrap();
        //     // let current_time = timeutil::convert_datetime_to_timestamp(&val.tapidtstamp);
        //     // let exist_time = timeutil::convert_datetime_to_timestamp(&exist_tick.tapidtstamp);
        //     // if current_time >= exist_time {
        //     //     tick_map.insert(val.contract_no1.clone(), val.clone());
        //     // }
        // }

        // let now = std::time::Instant::now();
        // if let Err(err) = self.sledclient.batch_insert(&tick_map).await {
        //     error!("{:?}", err);
        //     return Err(anyhow!(err));
        // }
        // info!("Tick数据写入本地缓存完成,数据量:{},用时:{:?}", tick_map.len(), now.elapsed());

        let mut tick_map: HashMap<String, YsHqInfo> = HashMap::with_capacity(ticks.len());
        for val in ticks {
            let current_time = timeutil::convert_datetime_to_timestamp(&val.tapidtstamp);
            match tick_map.get(&val.contract_no1) {
                Some(existing_tick) => {
                    let exist_time = timeutil::convert_datetime_to_timestamp(&existing_tick.tapidtstamp);
                    if current_time < exist_time {
                        continue;
                    }
                }
                None => {}
            }
            tick_map.insert(val.contract_no1.clone(), val.clone());
        }

        let now = std::time::Instant::now();
        if let Err(err) = self.sledclient.batch_insert(&tick_map).await {
            error!("{:?}", err);
            return Err(anyhow!(err));
        }
        info!("Tick数据写入本地缓存完成,数据量:{},用时:{:?}", tick_map.len(), now.elapsed());
        Ok(())
    }

    pub async fn insert_ticks(&self, tick: &mut YsHqInfo) -> Result<()> {
        self.tick_ctl.insert_ticks(tick, &self.common_util).await
    }

    //最新价
    pub async fn get_stock_last_price(&self, req: &LastPriceReq) -> Result<LastPriceResp> {
        info!("get_stock_last_price request:{:?} ", req.contract_nos);

        let mut res = LastPriceResp {
            err_msg: errors::get_error_code(errors::ErrorCode::CodeOk).0,
            err_code: errors::get_error_code(errors::ErrorCode::CodeOk).1,
            data: None,
        };
        match self.tick_ctl.query_tick(&req.contract_nos).await {
            Some(d) => {
                res.data = Some(d);
            }
            None => {
                res.err_msg = errors::get_error_code(errors::ErrorCode::CodeNoData).0;
                res.err_code = errors::get_error_code(errors::ErrorCode::CodeNoData).1;
            }
        }

        info!("get_stock_last_price response:{:?}", res);

        Ok(res)
    }

    pub async fn get_tick_hq(&self, req: &TickReq) -> Result<TickResp> {
        info!("最新tick请求: {}", &req.contract_no);
        let mut resp = TickResp::default();
        resp.tick_hq_info = self.tick_ctl.get_ticks(&req.contract_no).await;
        Ok(resp)
    }

    // pub async fn insert_cache(&self, tick: &mut YsHqInfo) -> Result<()> {
    //     if !self.tick_ctl.check_trade_day(&tick.exchange_id).await {
    //         return Err(anyhow!("当前日期不是交易日,不处理: code: {}, time: {}", tick.contract_no1, tick.tapidtstamp));
    //     }
    //     let (ret_time, flag) = self.common_util.check_hqtick_time(&tick.tapidtstamp[11..19].to_owned(), &tick.exchange_id).await;

    //     if flag < 0 && (self.common_util.trade_area_index(&tick.tapidtstamp[11..19].to_owned(), &tick.exchange_id).await).is_err() {
    //         return Err(anyhow!("该条tick不在交易时间内,不处理: code: {}, time: {}", tick.contract_no1, tick.tapidtstamp));
    //     }
    //     if flag == 2 || flag == 3 {
    //         tick.tapidtstamp = tick.tapidtstamp[0..11].to_owned() + &ret_time;
    //     }

    //     {
    //         self.ticks_cache.write().await.push(tick.to_owned());
    //     }
    //     Ok(())
    // }
}
