use anyhow::{anyhow, Result};
use common::logclient::log_error;
use futures::stream::{FuturesUnordered, StreamExt};
use scylla::{
    batch::{Batch, BatchType},
    prepared_statement::PreparedStatement,
    transport::session::PoolSize,
    Session, SessionBuilder,
};
// use scylla::client::session::Session;
// use scylla::client::session_builder::SessionBuilder;
// use scylla::client::PoolSize;
// use scylla::statement::batch::Batch;
// use scylla::statement::prepared::PreparedStatement;
use std::{num::NonZeroUsize, sync::Arc};
use tracing::{error, info};

use crate::server::service::persistservice::{CassandraData, CassandraKLineData, FenShiData, PersistService};

#[allow(dead_code)]
pub struct CassandraClient {
    pub namespace: String,
    pub session: Arc<Session>, //该连接用于执行Sql语句
}

impl CassandraClient {
    pub async fn new(addr: &String, username: &String, password: &String, namespace: &String) -> Result<Self> {
        match SessionBuilder::new()
            .known_node(addr)
            .user(username, password)
            .use_keyspace(namespace, false)
            .pool_size(PoolSize::PerHost(NonZeroUsize::new(16).unwrap()))
            .keepalive_interval(std::time::Duration::from_secs(2))
            .build()
            .await
        {
            Ok(session) => {
                info!("Cassandra 连接成功...");
                // session.use_keyspace(namespace.clone(), false).await.expect("user namespace error......");

                let client = CassandraClient {
                    namespace: namespace.clone(),
                    session: Arc::new(session),
                };
                return Ok(client);
            }
            Err(e) => {
                error!("Cassandra 连接失败: {}", e);
                log_error(&format!("Cassandra 连接失败: {}, {}", addr, e)).await;
                return Err(anyhow!("Cassandra 连接失败: {}", e));
            }
        }
    }

    #[allow(dead_code)]
    pub async fn do_prepare(&self, cql: &str) -> Result<PreparedStatement> {
        let ret = self.session.prepare(cql).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("prepare cql error"));
        }
        Ok(ret.unwrap())
    }

    #[allow(dead_code)]
    pub async fn execute_prepare(&self, prepared: &PreparedStatement, val: &FenShiData) -> Result<()> {
        let ret = self.session.execute_unpaged(&prepared, &val).await;
        // let ret = self.session.query(cql, &[]).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn execute_cql(&self, cql: &str, val: &CassandraData) -> Result<()> {
        // self.session.prepare(&cql).await;
        let ret = self.session.prepare(cql).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("prepare cql error"));
        }
        let prepared = ret.unwrap();
        let ret = self.session.execute_unpaged(&prepared, &val).await;
        // let ret = self.session.query(cql, &[]).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        }
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn execute_batch_fenshi(&self, cql: &str, fenshi: &Vec<FenShiData>) -> Result<()> {
        let prepared = match self.session.prepare(cql).await {
            Ok(stmt) => stmt,
            Err(e) => return Err(anyhow!("prepare cql error: {}", e)),
        };

        for chunk in fenshi.chunks(6) {
            let mut batch: Batch = Default::default();
            for _ in chunk {
                batch.append_statement(prepared.clone());
            }
            // 直接传 chunk 作为参数绑定
            if let Err(err) = self.session.batch(&batch, chunk).await {
                error!("Cassandra 批量插入失败: {:?}", err);
                error!("批量数据: {:?}", chunk);
                continue;
            }
        }
        Ok(())
    }

    #[allow(dead_code)]
    pub async fn execute_batch_k(&self, parms: &Vec<CassandraKLineData>) -> Result<()> {
        let mut values: Vec<()> = Vec::new();
        for val in parms.chunks(8) {
            let mut batch: Batch = Default::default();
            for data in val.iter() {
                let table_name = PersistService::get_table_name(data.peroid);
                let cql = format!(
                    "insert into {} (en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume, vc_code, l_update_time, l_fixed_time) values ({:.03}, {:.03}, {:.03}, {:.03}, {:.03}, {}, '{}', {}, toTimestamp(now()))",
                    table_name, data.en_close_price, data.en_high_price, data.en_last_price, data.en_low_price, data.en_open_price, data.l_volume as i64, data.vc_code, data.l_update_time,
                );
                // info!("{}", &cql);
                batch.append_statement(cql.as_str());
                values.push(());
            }
            if let Err(err) = self.session.batch(&batch, values.clone()).await {
                error!("{:?}", err);
                error!("{:?}", val);
                values.clear();
                continue;
            }
            values.clear();
        }
        Ok(())
    }

    pub async fn batch_insert_kline_per_table(&self, data: &[CassandraKLineData]) -> Result<()> {
        use std::collections::HashMap;
        let batch_size = 8; // 可调优
        let concurrency = 4; // 并发批次数

        // 1. 按表名分组（假设 get_table_name 是同步的，若异步可提前 await 收集）
        let mut table_map: HashMap<String, Vec<CassandraKLineData>> = HashMap::new();
        for row in data {
            let table_name = PersistService::get_table_name(row.peroid); // 建议同步
            table_map.entry(table_name).or_default().push(row.clone());
        }

        // 2. 预编译每个表的 insert 语句
        let mut prepared_map: HashMap<String, PreparedStatement> = HashMap::new();
        for table_name in table_map.keys() {
            let cql = format!(
                "INSERT INTO {} (en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume, vc_code, l_update_time, l_fixed_time) \
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, toTimestamp(now()))",
                table_name
            );
            let prepared = self.session.prepare(cql).await.map_err(|e| anyhow!("prepare cql error: {e}"))?;
            prepared_map.insert(table_name.clone(), prepared);
        }

        // 3. 并发批量写入
        let mut tasks = FuturesUnordered::new();

        for (table_name, rows) in table_map {
            let prepared = prepared_map.get(&table_name).unwrap().clone();
            let session = self.session.clone();

            for chunk in rows.chunks(batch_size) {
                let prepared = prepared.clone();
                let session = session.clone();
                let chunk = chunk.to_vec();

                tasks.push(tokio::spawn(async move {
                    let mut batch = Batch::new(BatchType::Logged);
                    let mut values = Vec::with_capacity(chunk.len());

                    for row in chunk {
                        batch.append_statement(prepared.clone());
                        values.push((
                            row.en_close_price as f32,
                            row.en_high_price as f32,
                            row.en_last_price as f32,
                            row.en_low_price as f32,
                            row.en_open_price as f32,
                            row.l_volume as i32,
                            row.vc_code.clone(),
                            row.l_update_time.parse::<i64>().unwrap_or_default(),
                        ));
                    }

                    session.batch(&batch, values).await.map_err(|e| anyhow!("batch error: {e}"))
                }));

                // 控制并发
                if tasks.len() >= concurrency {
                    if let Some(res) = tasks.next().await {
                        res??;
                    }
                }
            }
        }

        // 等待剩余任务
        while let Some(res) = tasks.next().await {
            res??;
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn delete_data(&self, stock: &Vec<String>, table_name: &String, start_time: &String, end_time: &String) -> Result<()> {
        // 创建连接会话
        // 构建删除查询
        // 执行删除操作
        let mut values: Vec<()> = Vec::new();
        let dst: Vec<Vec<String>> = stock.chunks(12).map(|s| s.into()).collect();
        for val in dst.iter() {
            let mut batch: Batch = Default::default();
            for x in val.iter() {
                let cql = format!("DELETE FROM {} WHERE vc_code = '{}' and l_update_time >= {} and l_update_time <= {}", table_name, x, start_time, end_time);
                batch.append_statement(cql.as_str());
                values.push(());
            }
            if let Err(err) = self.session.batch(&batch, values.clone()).await {
                error!("{:?}", err);
                error!("{:?}", val);
                // continue
                return Err(anyhow!("{:?}", err));
            }
            values.clear();
        }
        Ok(())
    }

    /// end_time 20241122093000
    #[allow(dead_code)]
    pub async fn delete_kline(&self, vc_code: &Vec<String>, namespace: &String, table_name: &String, start_time: &String, end_time: &String) -> Result<()> {
        let mut values: Vec<()> = Vec::new();
        let dst: Vec<Vec<String>> = vc_code.chunks(12).map(|s| s.into()).collect();
        for val in dst.iter() {
            let mut batch: Batch = Default::default();
            for x in val.iter() {
                // let cql = format!(
                //     "DELETE FROM {}.{} WHERE vc_code = '{}' and l_update_time >= {} and l_update_time <= {}",
                //     namespace, table_name, x, start_time, end_time
                // );
                let cql = if !end_time.is_empty() {
                    format!(
                        "DELETE FROM {}.{} WHERE vc_code = '{}' and l_update_time >= {} and l_update_time <= {}",
                        namespace, table_name, x, start_time, end_time
                    )
                } else {
                    format!("DELETE FROM {}.{} WHERE vc_code = '{}' and l_update_time <= {}", namespace, table_name, x, start_time)
                };
                let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
                batch.append_statement(prepared);
                values.push(());
            }

            // 准备批处理，添加错误处理
            let prepared_batch = self.session.prepare_batch(&batch).await.map_err(|err| {
                error!("批处理准备失败: {:?}", err);
                anyhow!(err)
            })?;

            // 执行批处理，添加错误处理
            self.session.batch(&prepared_batch, &values).await.map_err(|err| {
                error!("批处理执行失败: {:?}", err);
                anyhow!(err)
            })?;

            values.clear();
        }
        Ok(())
    }

    /// end_time: 20241128
    #[allow(dead_code)]
    pub async fn delete_time_line(&self, vc_code: &Vec<String>, namespace: &String, table_name: &String, start_time: &String, end_time: &String) -> Result<()> {
        let mut values: Vec<()> = Vec::new();
        let dst: Vec<Vec<String>> = vc_code.chunks(12).map(|s| s.into()).collect();
        for val in dst.iter() {
            let mut batch: Batch = Default::default();
            for x in val.iter() {
                // let cql = format!(
                //     "DELETE FROM {}.{} WHERE vc_code = '{}' and l_update_time >= {} and l_update_time <= {}",
                //     namespace, table_name, x, start_time, end_time
                // );
                let cql = if !end_time.is_empty() {
                    format!(
                        "DELETE FROM {}.{} WHERE vc_code = 'STOCK_FS:{}' and l_update_time >= {} and l_update_time <= {}",
                        namespace, table_name, x, start_time, end_time
                    )
                } else {
                    format!("DELETE FROM {}.{} WHERE vc_code = 'STOCK_FS:{}' and l_update_time <= {}", namespace, table_name, x, start_time)
                };
                let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
                batch.append_statement(prepared);
                values.push(());
            }

            // 准备批处理，添加错误处理
            let prepared_batch = self.session.prepare_batch(&batch).await.map_err(|err| {
                error!("批处理准备失败: {:?}", err);
                anyhow!(err)
            })?;

            // 执行批处理，添加错误处理
            self.session.batch(&prepared_batch, &values).await.map_err(|err| {
                error!("批处理执行失败: {:?}", err);
                anyhow!(err)
            })?;

            values.clear();
        }
        Ok(())
    }

    /// end_time: 1732267502
    #[allow(dead_code)]
    pub async fn delete_tick(&self, vc_contract_code: &Vec<String>, namespace: &String, table_name: &String, start_time: &String, end_time: &String) -> Result<()> {
        let mut values: Vec<()> = Vec::new();
        let dst: Vec<Vec<String>> = vc_contract_code.chunks(12).map(|s| s.into()).collect();
        for val in dst.iter() {
            let mut batch: Batch = Default::default();
            for x in val.iter() {
                // let cql = format!(
                //     "DELETE FROM {}.{} WHERE vc_contract_code = '{}' and l_update_time >= '{}' and l_update_time <= '{}'",
                //     namespace, table_name, x, start_time, end_time
                // );
                let cql = if !end_time.is_empty() {
                    format!(
                        "DELETE FROM {}.{} WHERE vc_contract_code = '{}' and l_update_time >= '{}' and l_update_time <= '{}'",
                        namespace, table_name, x, start_time, end_time
                    )
                } else {
                    format!("DELETE FROM {}.{} WHERE vc_contract_code = '{}' and l_update_time <= '{}'", namespace, table_name, x, start_time)
                };
                let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
                batch.append_statement(prepared);
                values.push(());
            }

            // 准备批处理，添加错误处理
            let prepared_batch = self.session.prepare_batch(&batch).await.map_err(|err| {
                error!("批处理准备失败: {:?}", err);
                anyhow!(err)
            })?;

            // 执行批处理，添加错误处理
            self.session.batch(&prepared_batch, &values).await.map_err(|err| {
                error!("批处理执行失败: {:?}", err);
                anyhow!(err)
            })?;

            values.clear();
        }
        Ok(())
    }
}
