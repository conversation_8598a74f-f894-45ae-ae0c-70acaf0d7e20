use anyhow::Result;
use chrono::Timelike;
use dashmap::DashMap;
use lazy_static::lazy_static;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{error, info};

use akaclient::akaclient::AkaClient;
use common::logclient::LogClient;
use protoes::hqmsg::YsHqInfo;
use utility::timeutil::{build_naive_date_time, from_timestamp_to_naive_date_time, today_weekend};

use super::klineservice::KLineService;
use crate::{commonutil::commonutil::CommonUtil, server::service::klineservice::TimePair};

//主要功能：
//1)保存到tick缓存
//2)持久化tick到kv存储
lazy_static! {
    static ref SH_DATE_FLAG: RwLock<bool> = RwLock::new(true);
    static ref SZ_DATE_FLAG: RwLock<bool> = RwLock::new(true);
    static ref HK_DATE_FLAG: RwLock<bool> = RwLock::new(true);
}

#[derive(Clone, Debug)]
#[allow(dead_code)]
pub struct TickService {
    pub akaclient: Arc<AkaClient>,
    ticks_cache: Arc<DashMap<String, YsHqInfo>>,
}

#[allow(dead_code)]
impl TickService {
    pub fn new(akaclient: Arc<AkaClient>) -> Self {
        Self {
            akaclient,
            ticks_cache: Arc::new(DashMap::new()),
        }
    }

    pub async fn get_market_id(exchange_code: &String) -> i64 {
        match exchange_code.as_str() {
            "XSHG" | "HS" => 101,
            "XSHE" => 102,
            "XHKG" | "HK" => 103,
            "XASE" => 104,
            "XNYS" => 105,
            "XNAS" => 106,
            _ => 0,
        }
    }

    ///检查市场交易日
    pub async fn check_trade_day(&self, exchange_id: &String) -> bool {
        let market_id = Self::get_market_id(exchange_id).await;
        let ret = self.akaclient.query_market_info(market_id).await;
        if ret.is_err() {
            error!("获取交易日信息失败: {}", ret.err().unwrap());
            if let Ok(client) = LogClient::get() {
                client.push_error(&format!("获取交易日信息失败")).await;
            }
            // return Err(anyhow!("获取交易日信息失败: {}", ret.err().unwrap()));
            return false;
        }
        let market_info = ret.unwrap();
        // info!("market_info: {:?}", market_info);
        if market_id == 101 {
            let mut flag = SH_DATE_FLAG.write().await;
            if *flag {
                info!("获取沪深交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else if market_id == 102 {
            let mut flag = SZ_DATE_FLAG.write().await;
            if *flag {
                info!("获取沪深交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else if market_id == 103 {
            let mut flag = HK_DATE_FLAG.write().await;
            if *flag {
                info!("获取港股交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else {
            info!("ret: {:?}", market_info);
        }

        if market_info.date_type == 0 || today_weekend() {
            error!("当前时间不是交易日：{}", market_info.current_date);
            return false;
        }

        true
    }

    pub async fn insert_ticks(&self, hqinfo: &mut YsHqInfo, common_util: &CommonUtil, kline_ctl: &KLineService) -> Result<()> {
        if !self.check_trade_day(&hqinfo.exchange_id).await {
            return Err(anyhow!("当前日期不是交易日,不处理: code: {}, time: {}", hqinfo.contract_no1, hqinfo.tapidtstamp));
        }

        let hq_time = &hqinfo.tapidtstamp[11..19];
        // 检查时间
        let (ret_time, flag) = common_util.check_hqtick_time(hq_time, &hqinfo.exchange_id).await;
        let new_time = hqinfo.tapidtstamp[0..11].to_owned() + &ret_time; //yyyy-mm-dd 09:30:00

        // 检查交易时间
        if flag < 0 && common_util.trade_area_index(hq_time, &hqinfo.exchange_id).await.is_err() {
            return Err(anyhow!("该条tick不在交易时间内,不处理: code: {}, time: {}", hqinfo.contract_no1, hqinfo.tapidtstamp));
        }
        // K线相关
        let st = if ret_time.is_empty() {
            build_naive_date_time(&hqinfo.tapidtstamp)
        } else {
            build_naive_date_time(&new_time)
        };
        let timestamp = st.and_utc().timestamp();

        match flag {
            1 => {
                self.ticks_cache.insert(hqinfo.contract_no1.clone(), hqinfo.clone());
                let adjusted_time = from_timestamp_to_naive_date_time(timestamp - 30);
                let time_pair = TimePair {
                    minute: (adjusted_time.hour() * 60 + adjusted_time.minute()).into(),
                    timestamp: adjusted_time.and_utc().timestamp(),
                };
                kline_ctl.contract_fill.insert(hqinfo.contract_no1.clone(), time_pair);
                TickService::before_disc_data(hqinfo, common_util, kline_ctl).await;
                return Err(anyhow!("开盘集合竞价数据不发送MQ..."));
            }
            2 | 3 => {
                //午休tick //闭市集合竞价tick
                //yyyy-mm-dd 14:59:59, yyyy-mm-dd 11:29:59
                //闭市把时间戳处理成14:59:59.999, 15:59:59.999
                //午休11:29:59
                let adjusted_timestamp = timestamp - 1; // 处理午休和闭市
                let adjusted_time = from_timestamp_to_naive_date_time(adjusted_timestamp);
                hqinfo.tapidtstamp = format!("{}.999", adjusted_time);

                let mut time_pair = TimePair { minute: 0, timestamp: adjusted_timestamp };
                match flag {
                    2 => {
                        time_pair.minute = if hqinfo.exchange_id == "XHKG" {
                            (adjusted_time.hour() * 60 + adjusted_time.minute() + 2).into()
                        } else {
                            (adjusted_time.hour() * 60 + adjusted_time.minute() + 1).into()
                        };
                    }
                    3 => {
                        time_pair.minute = match hqinfo.exchange_id.as_str() {
                            "XSHG" | "XSHE" => (adjusted_time.hour() * 60 + adjusted_time.minute() + 2).into(),                         //yyyy-mm-dd 15:00
                            "XHKG" => (adjusted_time.hour() * 60 + adjusted_time.minute() + 10).into(),                                 //防止超过一分钟没有tick造成补数据开始补16:00:00的数据
                            "XASE" | "XNYS" | "XNAS" => (adjusted_time.hour() * 60 + adjusted_time.minute() + 1 - 12 * 60 + 30).into(), //04:30
                            _ => 0,
                        };
                    }
                    _ => {}
                }

                // if flag == 2 {
                //     time_pair.minute = if hqinfo.exchange_id == "XHKG" {
                //         //11:30
                //         (adjusted_time.hour() * 60 + adjusted_time.minute() + 2).into()
                //     } else {
                //         //11:30
                //         (adjusted_time.hour() * 60 + adjusted_time.minute() + 1).into()
                //     };
                // } else if flag == 3 {
                //     if hqinfo.exchange_id == "XSHG" || hqinfo.exchange_id == "XSHE" {
                //         //yyyy-mm-dd 15:00
                //         time_pair.minute = (adjusted_time.hour() * 60 + adjusted_time.minute() + 2).into();
                //     } else if hqinfo.exchange_id == "XHKG" {
                //         //防止超过一分钟没有tick造成补数据开始补16:00:00的数据
                //         time_pair.minute = (adjusted_time.hour() * 60 + adjusted_time.minute() + 10).into();
                //     } else if hqinfo.exchange_id == "XASE" || hqinfo.exchange_id == "XNYS" || hqinfo.exchange_id == "XNAS" {
                //         //04:30
                //         time_pair.minute = (adjusted_time.hour() * 60 + adjusted_time.minute() + 1 - 12 * 60 + 30).into();
                //     }
                // }
                kline_ctl.contract_fill.insert(hqinfo.contract_no1.clone(), time_pair);
            }
            _ => {
                // 交易时间(非特殊时间)
                let time_pair = TimePair {
                    minute: (st.hour() * 60 + st.minute()).into(),
                    timestamp,
                };
                kline_ctl.contract_fill.insert(hqinfo.contract_no1.clone(), time_pair);
            }
        }

        self.ticks_cache.insert(hqinfo.contract_no1.to_owned(), hqinfo.to_owned());
        Ok(())
    }

    async fn before_disc_data(hqinfo: &YsHqInfo, common_util: &CommonUtil, kline_ctl: &KLineService) {
        let kline_types = [
            crate::server::service::common::KLineType::Hq1Kline,
            crate::server::service::common::KLineType::Hq5Kline,
            crate::server::service::common::KLineType::Hq10Kline,
            crate::server::service::common::KLineType::Hq30Kline,
            crate::server::service::common::KLineType::Hq60Kline,
            crate::server::service::common::KLineType::Hq24Kline,
        ];

        for &kline_type in &kline_types {
            kline_ctl.insert_or_update(hqinfo, kline_type, common_util).await;
        }
    }

    pub async fn query_tick(&self, key: &String) -> Option<YsHqInfo> {
        let hqinfo = self.ticks_cache.get(key);
        if hqinfo.is_none() {
            return None;
        }
        Some(hqinfo.unwrap().value().to_owned())
    }
}
