use anyhow::Result;
use tonic::transport::Channel;
use tonic::Request;
use tracing::*;

use protoes::phoenixklinecenter::{phoenix_kline_center_client::PhoenixKlineCenterClient, FenShiResp, KLineDataResp, KLineHqRequest};

#[derive(Clone)]
pub struct KlineCenterClient {
    uri_hs: String,
    uri_hk: String,
    pub client_hs: Option<PhoenixKlineCenterClient<Channel>>,
    pub client_hk: Option<PhoenixKlineCenterClient<Channel>>,
}

impl KlineCenterClient {
    pub async fn new(uri_hs: &String, uri_hk: &String) -> Self {
        info!("HS_KLINE server: {}, HK_KLINE server: {}", uri_hs, uri_hk);
        let mut kline_client = KlineCenterClient {
            uri_hs: uri_hs.to_owned(),
            uri_hk: uri_hk.to_owned(),
            client_hs: None,
            client_hk: None,
        };
        let ret = PhoenixKlineCenterClient::connect(uri_hs.to_owned()).await;
        if ret.as_ref().is_err() {
            // push_log(format!("connect to HS_KLINE failed: {:?}", uri).as_str()).await;
            error!("HS_KLINE连接失败: {:?}", ret);
        } else {
            info!("HS_KLINE连接成功....");
            kline_client.client_hs = Some(ret.unwrap());
        }

        let ret = PhoenixKlineCenterClient::connect(uri_hk.to_owned()).await;
        if ret.as_ref().is_err() {
            // push_log(format!("connect to HK_KLINE failed: {:?}", uri).as_str()).await;
            error!("HK_KLINE连接失败: {:?}", ret);
        } else {
            info!("HK_KLINE连接成功....");
            kline_client.client_hk = Some(ret.unwrap());
        }
        kline_client
    }

    pub async fn init_client_hs(&mut self) -> Result<PhoenixKlineCenterClient<Channel>> {
        if self.client_hs.is_some() {
            return Ok(self.client_hs.clone().unwrap());
        } else {
            let ret = PhoenixKlineCenterClient::connect(self.uri_hs.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to HS_KLINE failed: {:?}", self.uri_hs).as_str()).await;
                return Err(anyhow!(format!("connect to HS_KLINE failed")));
            }
            let client = ret.expect("connect to HS_KLINE failed");
            info!("HS_KLINE连接成功....");
            self.client_hs = Some(client);
            return Ok(self.client_hs.clone().unwrap());
        }
    }

    pub async fn init_client_hk(&mut self) -> Result<PhoenixKlineCenterClient<Channel>> {
        if self.client_hk.is_some() {
            return Ok(self.client_hk.clone().unwrap());
        } else {
            let ret = PhoenixKlineCenterClient::connect(self.uri_hk.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to HK_KLINE failed: {:?}", self.uri_hs).as_str()).await;
                return Err(anyhow!(format!("connect to HK_KLINE failed")));
            }
            let client = ret.expect("connect to HK_KLINE failed");
            info!("HK_KLINE连接成功....");
            self.client_hk = Some(client);
            return Ok(self.client_hk.clone().unwrap());
        }
    }

    pub async fn get_last_kline_data(&mut self, contract_no: &String, kline_type: &String) -> Result<KLineDataResp> {
        let ret = self.get_connect(contract_no).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (flag, mut client) = ret.unwrap();

        let req_data = KLineHqRequest {
            contract_no: contract_no.to_owned(),
            kline_type: kline_type.to_owned(),
        };
        match client.get_last_kline_data(Request::new(req_data)).await {
            Ok(value) => {
                return Ok(value.into_inner());
            }
            Err(err) => {
                if flag == 1 {
                    self.client_hs = None;
                } else {
                    self.client_hk = None;
                }
                error!("获取最新K线失败: {:?}", err);
                return Err(anyhow!("获取最新K线失败: {:?}", err));
            }
        }
    }

    pub async fn get_generate_fenshi_hq(&mut self, contract_no: &String, kline_type: &String) -> Result<FenShiResp> {
        let ret = self.get_connect(contract_no).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (flag, mut client) = ret.unwrap();

        let req_data = KLineHqRequest {
            contract_no: contract_no.to_owned(),
            kline_type: kline_type.to_owned(),
        };
        match client.get_generate_fenshi_hq(Request::new(req_data)).await {
            Ok(value) => {
                return Ok(value.into_inner());
            }
            Err(err) => {
                if flag == 1 {
                    self.client_hs = None;
                } else {
                    self.client_hk = None;
                }
                error!("获取分时数据失败: {:?}", err);
                return Err(anyhow!("获取分时失败: {:?}", err));
            }
        }
    }

    async fn get_connect(&mut self, contract_no: &String) -> Result<(i64, PhoenixKlineCenterClient<Channel>)> {
        let mut flag = 1;
        let ret = self.init_client_hs().await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();
        if contract_no.contains("XHKG") || contract_no.contains("HK") {
            let ret = self.init_client_hk().await;
            if ret.as_ref().is_err() {
                error!("{:?}", ret.as_ref().err().unwrap());
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            client = ret.unwrap();
            flag = 2;
        }
        return Ok((flag, client));
    }
}
