// #![allow(dead_code, unused_imports)] //功能:没有使用的代码或模块不警告
#[macro_use]
extern crate anyhow;
extern crate chrono;

mod client;
mod config;
mod dataservice;
mod odcommon;
// mod protofiles;
mod server;

use crate::config::settings::Settings;
use crate::server::server::OrderServerHandler;
use anyhow::Result;
use common::{init_tracing, logclient::*};
use protoes::phoenixordercenter::order_center_service_server::OrderCenterServiceServer;
// use std::process;
use std::time::Duration;
use tracing::info;
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let prefix = "phoenix_ordercenter";
    let dir = "./log";

    let settings = Settings::new().expect("read config error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", settings);

    init_logclient(&settings.servers.logcenterserver, &format!("{}_phoenix_ordercenter", &settings.redis.prefix)).await;

    let server = OrderServerHandler::new(&settings).await.expect("init server error");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.ip,
        settings.application.port,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{},name: {} version: {} description: {}",
        settings.application.ip,
        settings.application.port,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;
    run_server(server).await

    // match ret {
    //     Ok(server) => run_server(server).await,
    //     Err(err) => {
    //         error!("{:?} 服务启动失败...", err);
    //         if let Ok(c) = LogClient::get() {
    //             c.push_error(&format!("服务启动失败:{}", err.to_string())).await;
    //         }
    //         process::exit(1);
    //     }
    // }
}

async fn run_server(mut server: OrderServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.ip, server.settings.application.port);
    let addr = app_url.as_str().parse().unwrap();

    // info!("Starting orderceter service on: {}", addr);
    // info!(app_url, "name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!(
    //     "server started, name: {} version: {} description: {}",
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // ))
    // .await;
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .timeout(Duration::from_millis(5000)) //设置全部请求超时时间,超过时间直接返回
        .add_service(OrderCenterServiceServer::new(server))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    log_debug(&format!("phoenix_ordercenter service is shutted down")).await;
    Ok(())
}
