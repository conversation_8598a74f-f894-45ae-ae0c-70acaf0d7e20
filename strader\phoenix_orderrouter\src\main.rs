// #[macro_use]
extern crate anyhow;
extern crate chrono;
extern crate lazy_static;
mod config;
// mod protofiles;
mod server;
mod service;
use crate::config::settings::Settings;
use crate::server::server::RouterServerHandler;
use anyhow::Result;
use common::logclient::*;
use protoes::phoenixorderrouter::order_router_service_server::OrderRouterServiceServer;
// use server::server::{IN_BP_CACHE, OUT_BP_CACHE};
use tracing::*;
// use utility::loggings;

#[tokio::main]
async fn main() -> Result<()> {
    // let cfg = "config/orderrouter.yaml";
    // loggings::log_init(cfg);

    let prefix = "phoenix_orderrouter";
    let dir = "./log";

    let settings = Settings::new().expect("init setting error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    //触发lazy static初始化
    // info!("OUT_BP_CACHE {:?}", *OUT_BP_CACHE);
    // info!("IN_BP_CACHE {:?}", *IN_BP_CACHE);

    init_logclient(&settings.servers.logcenterserver, "phoenix_orderrouter").await;

    let server = RouterServerHandler::new(&settings).await;
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    run_server(server).await.expect("server run error");

    Ok(())
}

async fn run_server(mut server: RouterServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport);
    let addr = app_url.as_str().parse().expect("parse ip address error");

    // info!("Starting order_router service on: {}", addr);
    // info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));
    // log_debug(&format!(
    //     "server started, name: {} version: {} description: {}",
    //     env!("CARGO_PKG_NAME"),
    //     env!("CARGO_PKG_VERSION"),
    //     env!("CARGO_PKG_DESCRIPTION")
    // ))
    // .await;

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });
    //创建可以配置[server]的新服务器生成器。
    tonic::transport::Server::builder()
        .tcp_keepalive(Some(std::time::Duration::from_secs(60)))
        .add_service(OrderRouterServiceServer::new(server))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
