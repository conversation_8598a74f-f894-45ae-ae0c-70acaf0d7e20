use tokio::sync::broadcast::Sender;
use tracing::*;

use protoes::hqmsg::YsHqInfo;

use crate::client::DbClient;
use crate::common::{convert_local_stock_info, StockInfo};
use crate::config::settings::Settings;
use crate::dataservice::entities::prelude::{SysCommodity, SysCommodityExt, SysCommodityGroup, SysCommodityGroupList, SysCommodityTmp};
use crate::fiuhq::{self, FiuApi, IndustryList};

#[allow(dead_code)]
#[derive(Clone)]
pub struct FiuHqController {
    setting: Settings,
    pub fiu_api: FiuApi,
    // db: Option<DbClient>,
    pub pull_ex: i32,
}

impl FiuHqController {
    #[allow(dead_code)]
    pub async fn new(setting: &Settings) -> Self {
        let fiu_api = FiuApi::new(&setting.system.hsfiuapi, &setting.system.hkfiuapi.to_owned()).await;
        // let mut db = None;
        // if !setting.mysql.url.is_empty() {
        //     let dbcli = DbClient::new(&setting.mysql.url).await;
        //     db = Some(dbcli)
        // }

        Self {
            setting: setting.clone(),
            fiu_api,
            // db,
            pull_ex: 0,
        }
    }

    #[allow(dead_code)]
    pub async fn pull_code(&mut self, tx_tick: Sender<YsHqInfo>) {
        // if self.db.is_none() {
        //     return;
        // }
        // let db = self.db.clone().unwrap();

        // 股票 ETF 指数 基金
        let hs_stock = self.fiu_api.hs_post_stock_basic_info().await;
        let hk_stock = self.fiu_api.hk_post_stock_basic_info().await;
        let stock_info = convert_local_stock_info(&hs_stock, &hk_stock).await;
        info!("{:#?}", stock_info.len());

        let mut industry = self.fiu_api.hs_post_industry_list().await;
        let hk_industry = self.fiu_api.hk_post_industry_list().await;
        industry.extend(hk_industry);
        // info!("{:#?}", industry);

        self.ws_subscribe(tx_tick.clone(), &stock_info, &industry).await;

        for (key, value) in self.setting.mysql.iter() {
            if value.is_empty() {
                continue;
            }
            info!("pull code start {}：{}", key, value);
            let db = DbClient::new(&value).await;

            let _ = SysCommodityTmp::delete_from_exchange(&db /*, 1*/).await;
            // let _ = SysCommodityTmp::delete_from_exchange(&db, 2).await;
            let sys_commodity_tmp = SysCommodityTmp::convert_stock_basic_to_model(&stock_info).await;
            let _ = SysCommodityTmp::insert_many(&db, &sys_commodity_tmp).await;
            info!("Insert SysCommodityTmp finished");

            let sys_commodity = SysCommodity::select_all_model(&db).await;
            info!("{:#?}", sys_commodity.len());
            let mut new_stock_info = Vec::new();
            for v in stock_info.iter() {
                if sys_commodity.iter().find(|x| x.inner_code == v.inner_code).is_none() {
                    new_stock_info.push(v.to_owned())
                }
            }
            //未导入的股票
            let model = SysCommodity::convert_stock_basic_to_model(&new_stock_info).await;
            info!("新增: {:#?}", model.len());
            if !model.is_empty() {
                let _ = SysCommodity::insert_many(&db, &model).await;
                info!("Insert SysCommodity finished");
            }

            let mut sys_commodity = SysCommodity::select_all_model(&db).await;
            let ext = SysCommodityExt::to_model(&new_stock_info, &sys_commodity).await;
            info!("新增ext: {:#?}", ext.len());
            if !model.is_empty() {
                let _ = SysCommodityExt::insert_many(&db, &ext).await;
                info!("Insert SysCommodityExt finished");
            }

            sys_commodity.retain(|x| x.market_id == 101 || x.market_id == 102 || x.market_id == 103);
            update(&stock_info, &mut sys_commodity);
            info!("更新: {:#?}", sys_commodity.len());
            let _ = SysCommodity::update_many(&db, &sys_commodity).await;
            // let _ = SysCommodity::update(&db, &stock_info).await;

            let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
            industry.retain(|list| sys_commodity_group.iter().find(|group| group.code == list.symbol).is_none());
            if !industry.is_empty() {
                let models = SysCommodityGroup::convert_industry_to_model(&industry).await;
                let _ = SysCommodityGroup::insert_many(&db, &models).await;
            }
            let sys_commodity_group = SysCommodityGroup::select_all_model(&db).await;
            let sys_commodity = SysCommodity::select_all_model(&db).await;
            let mut group_list = SysCommodityGroupList::default();
            let mut models = Vec::new();
            for stock in stock_info.iter() {
                let ret = sys_commodity.iter().find(|x| x.inner_code == stock.inner_code);
                if ret.is_none() {
                    continue;
                }
                let commodity = ret.unwrap();

                for x in stock.plate_codes.iter() {
                    if let Some(group) = sys_commodity_group.iter().find(|y| x.to_uppercase() == y.code.to_uppercase()) {
                        group_list.commodity_id = commodity.id;
                        group_list.group_id = group.id;
                        models.push(group_list.to_owned());
                    }
                }
            }
            info!("{}", models.len());
            let old = SysCommodityGroupList::select_all_model(&db).await;
            models.retain(|x| old.iter().find(|y| x.group_id == y.group_id && x.commodity_id == y.commodity_id).is_none());
            info!("{:?}", models.len());
            if !models.is_empty() {
                let _ = SysCommodityGroupList::insert_many(&db, &models).await;
            }

            info!("pull code finished {}：{}", key, value);
        }

        self.pull_ex = 1;
    }

    pub async fn ws_subscribe(&self, tx_tick: Sender<YsHqInfo>, stock_info: &Vec<StockInfo>, industry: &Vec<IndustryList>) {
        let web = fiuhq::FiuWeb::new(&self.setting);
        let hk_web = web.clone();
        let hs_code = stock_info
            .iter()
            .filter(|x| x.outer_code.contains(".sh") || x.outer_code.contains(".sz"))
            .map(|x| x.clone())
            .collect::<Vec<StockInfo>>();
        let hk_code = stock_info.iter().filter(|x| x.outer_code.contains(".hk")).map(|x| x.clone()).collect::<Vec<StockInfo>>();

        let hs_industry = industry.iter().filter(|x| x.symbol.contains(".sh") || x.symbol.contains(".sz")).map(|x| x.clone()).collect::<Vec<IndustryList>>();
        let hk_industry = industry.iter().filter(|x| x.symbol.contains(".hk")).map(|x| x.clone()).collect::<Vec<IndustryList>>();
        info!("====={}", hs_industry.len());
        info!("======{}", hk_industry.len());

        let hs_tick = tx_tick.clone();
        let hk_tick = tx_tick.clone();
        tokio::spawn(async move {
            web.hsfiuweb(&hs_code, &hs_industry, hs_tick).await;
        });
        tokio::spawn(async move {
            hk_web.hkfiuweb(&hk_code, &hk_industry, hk_tick).await;
        });
    }
}

fn update(stockinfo: &Vec<StockInfo>, models: &mut Vec<SysCommodity>) {
    for v in stockinfo.iter() {
        let ret = models.iter_mut().find(|x| x.inner_code == v.inner_code);
        if ret.is_none() {
            continue;
        }
        let model = ret.unwrap();

        // 0正常  1未上市  2熔断  3停牌  4退市
        if v.security_status == 1 || v.security_status == 2 || v.security_status == 3 || v.security_status == 4 {
            model.show_state = 2;
            model.trade_state = 3;
            model.susp_state = Some(1);
        } else {
            model.show_state = 1;
            model.trade_state = 1;
            model.susp_state = Some(0);
        }
        model.modify_date = utility::timeutil::current_timestamp();

        if model.name != v.name {
            model.name = v.name.to_owned();
        }
        if model.hands_num != v.lot_size {
            model.hands_num = v.lot_size;
        }
        if model.currency != v.currency {
            model.currency = v.currency.to_owned();
        }
    }
}
