use anyhow::{anyhow, Result};
use lazy_static::lazy_static;
use std::collections::HashMap;
use std::fmt::Write;
use tokio::sync::RwLock;
use tonic::transport::Channel;
use tracing::*;

// use crate::server::service::push_log;

use protoes::{
    hqcenter::{KLineHqInfo, KLineHqReq, KLineHqResp},
    hqmsg::YsHqInfo,
    phoenixusserver::{us_server_client::UsServerClient, KLine, Security, TimePoint, UsKLineRequest, UsTickHqReq, UsTimeLineReq},
};

use crate::server::service::common::LastPrice;

lazy_static! {
    static ref USSECURITY: RwLock<HashMap<String, Security>> = RwLock::new(HashMap::new());
}

// #[derive(Clone)]
pub struct UsMartClient {
    pub client: Option<UsServerClient<Channel>>,
    uri: String,
}

impl UsMartClient {
    pub async fn new(uri: &String) -> Self {
        let mut usmart_client = UsMartClient { client: None, uri: uri.to_owned() };
        let ret = UsServerClient::connect(uri.to_owned()).await;
        if ret.as_ref().is_err() {
            // push_log(format!("connect to OrderRouter failed: {:?}", uri).as_str()).await;
            info!("USMART连接失败: {:?}", ret);
        } else {
            info!("USMART连接成功....");
            usmart_client.client = Some(ret.expect("connect to USMART failed"));
        }
        usmart_client
    }

    pub async fn init_client(&mut self) -> Result<UsServerClient<Channel>> {
        if self.client.is_some() {
            return Ok(self.client.clone().unwrap());
        } else {
            let ret = UsServerClient::connect(self.uri.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to OrderRouter failed: {:?}", self.uri).as_str()).await;
                return Err(anyhow!(format!("connect to USMART failed")));
            }
            let client = ret.expect("connect to USMART failed");
            info!("USMART连接成功....");
            self.client = Some(client);
            return Ok(self.client.clone().unwrap());
        }
    }

    pub async fn query_us_last_price(&mut self, stock_code: &String) -> Result<LastPrice> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();
        let request = UsTickHqReq { secu_ids: vec![stock_code.to_string()] };
        info!("us last price req: {:?}", request);
        match client.post_us_tick_hq(request).await {
            Ok(ret) => {
                let us_time_hq = ret.into_inner();
                // info!("{:?}", us_time_hq);
                if us_time_hq.code != 0 || us_time_hq.list.is_empty() {
                    info!("{:?}", us_time_hq);
                    if let Some(data) = USSECURITY.read().await.get(stock_code) {
                        let last_price = UsMartClient::convert_to_last_price(&data).await;
                        return Ok(last_price);
                    }
                    return Ok(LastPrice::default());
                }
                let us_tick = us_time_hq.list;
                USSECURITY.write().await.insert(stock_code.to_owned(), us_tick[0].to_owned());
                return Ok(UsMartClient::convert_to_last_price(&us_tick[0]).await);
            }
            Err(err) => {
                error!("{:?}", err);
                if let Some(data) = USSECURITY.read().await.get(stock_code) {
                    let last_price = UsMartClient::convert_to_last_price(&data).await;
                    return Ok(last_price);
                }
                return Err(anyhow!(err));
            }
        }
    }

    pub async fn query_us_fen_shi(&mut self, stock_code: &String) -> Result<String> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();
        let request = UsTimeLineReq {
            secu_id: stock_code.to_owned(),
            r#type: 0,
        };
        match client.post_us_current_fen_shi_hq(request).await {
            Ok(ret) => {
                let us_time_hq = ret.into_inner();
                if us_time_hq.code != 0 {
                    return Ok("".to_string());
                }
                return Ok(UsMartClient::convert_to_fen_shi(&us_time_hq.points).await);
            }
            Err(err) => {
                error!("{:?}", err);
                return Err(anyhow!(err));
            }
        }
    }

    pub async fn query_us_tick(&mut self, stock_code: &Vec<String>) -> Result<Vec<YsHqInfo>> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();

        let request = UsTickHqReq { secu_ids: stock_code.to_vec() };

        info!("query_us_tick: {:?}", request);
        match client.post_us_tick_hq(request.clone()).await {
            Ok(ret) => {
                let us_tick = ret.into_inner();
                if us_tick.code != 0 || us_tick.list.is_empty() {
                    info!("{:?}", us_tick);
                    return Ok(UsMartClient::get_ticks(&request.secu_ids).await);
                }
                // info!("post_us_tick_hq: {:?}", us_tick);

                return Ok(UsMartClient::convert_to_ys_hq_infos(&us_tick.list, &request.secu_ids).await);
            }
            Err(err) => {
                error!("{:?}", err);
                return Ok(UsMartClient::get_ticks(&request.secu_ids).await);
                // return Err(anyhow!(err))
            }
        }
    }

    pub async fn query_us_kline(&mut self, req: &KLineHqReq) -> Result<KLineHqResp> {
        let ret = self.init_client().await;
        if ret.as_ref().is_err() {
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();

        let mut request = UsKLineRequest::default();
        let kline_type: i32 = req.strklinetype.parse().unwrap_or_default(); //行情类型由字符串类型解析为i32类型
        request.secu_id = req.strcontractno.to_owned();
        request.r#type = UsMartClient::get_kline_type(kline_type).await;
        // request.start =
        // request.right =
        request.count = req.limit;
        info!("post_us_current_kline_hq: {:?}", request);
        match client.post_us_current_kline_hq(request).await {
            Ok(ret) => {
                let us_kline = ret.into_inner();
                if us_kline.code != 0 {
                    return Ok(KLineHqResp::default());
                }
                let mut kline = us_kline.klines;
                kline.sort_by(|x, y| y.latest_time.cmp(&x.latest_time));
                return Ok(UsMartClient::convert_to_kline(kline).await);
            }
            Err(err) => {
                error!("{:?}", err);
                return Err(anyhow!(err));
            }
        }
    }

    pub async fn convert_to_kline(klines: Vec<KLine>) -> KLineHqResp {
        let mut resp = KLineHqResp::default();
        let mut kline_hq_info = KLineHqInfo::default();
        for kline in klines.iter() {
            //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
            kline_hq_info.strkline = format!(
                "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                kline.close, kline.high, kline.close, kline.low, kline.open, kline.volume, kline.latest_time, kline.amount
            );
            resp.klineinfo.push(kline_hq_info.clone());
        }
        info!("query us kline: {:#?}", resp);
        resp
    }

    pub async fn convert_to_ys_hq_infos(securitys: &Vec<Security>, stocks: &Vec<String>) -> Vec<YsHqInfo> {
        let mut vec = Vec::new();
        for security in securitys.iter() {
            let mut tick = YsHqInfo::default();
            info!("{:?}", security);
            if let Some(stock) = stocks.iter().find(|x| x.contains(&security.symbol)) {
                let code = stock.split("_").collect::<Vec<&str>>();
                tick.exchange_id = code[1].to_string();
                tick.contract_no1 = format!("{}_{}", security.symbol, code[1].to_string());
                // tick.q_last_price = security.latest_price;
                if security.latest_price >= 1.0 {
                    tick.q_last_price = crate::server::service::common::rounding(security.latest_price, 2).await;
                } else {
                    tick.q_last_price = crate::server::service::common::rounding(security.latest_price, 3).await;
                }
                tick.q_opening_price = security.open;
                tick.q_low_price = security.low;
                tick.q_closing_price = security.close;
                tick.q_high_price = security.high;
                // 20231215160000000
                let year = security.latest_time / 10000000000000; //2023
                let month = security.latest_time / 100000000000; //202312
                let day = security.latest_time / 1000000000; //20231215
                let hour = security.latest_time / 10000000; //2023121516
                let minute = security.latest_time / 100000; //202312151600
                let second = security.latest_time / 1000; //20231215160000
                tick.tapidtstamp = format!(
                    "{:04}-{:02}-{:02} {:02}:{:02}:{:02}.{:03}",
                    year,
                    month % 100,
                    day % 100,
                    hour % 100,
                    minute % 100,
                    second % 100,
                    security.latest_time % 1000
                );
                tick.q_pre_closing_price = security.pre_close;
                tick.q_total_turnover = security.turnover;
                tick.q_total_qty = security.volume;
                tick.q_limit_up_price = security.up_limit;
                tick.q_limit_down_price = security.down_limit;
                tick.q_bid_qty.push(security.bid_size);
                tick.q_bid_price.push(security.bid_price);
                tick.q_ask_qty.push(security.ask_size);
                tick.q_ask_price.push(security.ask_price);
                if security.pre_close.ne(&0.0) {
                    tick.q_change_value = security.latest_price - security.pre_close;
                    tick.q_change_rate = (tick.q_change_value / security.pre_close) * 100 as f64;
                    tick.q_change_value = crate::server::service::common::rounding(tick.q_change_value, 2).await;
                    tick.q_change_rate = crate::server::service::common::rounding(tick.q_change_rate, 2).await;
                }
                // {
                //     if let Some(pre_us) = USSECURITY.read().await.get(&security.symbol) {
                //         tick.q_last_turnover = security.turnover - pre_us.turnover;
                //         tick.q_last_qty = (security.volume - pre_us.volume) as f64;
                //         if tick.q_last_qty.ne(&0.0){
                //             tick.q_average_price =  tick.q_last_turnover / tick.q_last_qty;
                //         }
                //         info!("q_last_turnover: {} - {} = {}", security.turnover, pre_us.turnover, tick.q_last_turnover);
                //         info!("q_last_qty: {} - {} = {}", security.volume, pre_us.volume, tick.q_last_qty);
                //         info!("q_average_price: {} / {} = {}", tick.q_last_turnover, tick.q_last_qty, tick.q_average_price);
                //     }
                // }
                {
                    USSECURITY.write().await.insert(tick.contract_no1.to_owned(), security.to_owned());
                }
                info!("{:?}", tick);
                vec.push(tick);
            }
        }
        vec
    }

    pub async fn convert_to_fen_shi(points: &Vec<TimePoint>) -> String {
        if points.is_empty() {
            return "".to_string();
        }
        let mut fenshis = points.clone();
        fenshis.sort_by(|a, b| a.latest_time.cmp(&b.latest_time));

        let mut us_fen_shi = String::from("");
        for val in fenshis.iter() {
            if !us_fen_shi.is_empty() {
                let delta_data = format!("{:<.03}|{:<.03}|{}|{}+", val.avg, val.price, val.volume, val.latest_time);
                let _ = write!(us_fen_shi, "{}", delta_data);
            } else {
                let delta_data = format!("{:<.03}|{:<.03}|{:<.03}|{}|{}+", val.pre_close, val.avg, val.price, val.volume, val.latest_time);
                us_fen_shi = delta_data;
            }
        }
        info!("美股分时: {}", us_fen_shi);
        us_fen_shi
    }

    pub async fn get_kline_type(kline_type: i32) -> i32 {
        match kline_type {
            1 => 1,  //1分钟K线
            5 => 2,  //5分钟K线
            10 => 3, //10分钟K线
            30 => 5, //30分钟K线
            60 => 6, //60分钟K线
            24 => 7, //1日K线
            _ => 0,
        }
    }

    pub async fn convert_to_last_price(securitys: &Security) -> LastPrice {
        info!("last price: {:?}", securitys);
        let mut last_price = LastPrice::default();
        last_price.last_price = if securitys.latest_price >= 1.0 {
            crate::server::service::common::rounding(securitys.latest_price, 2).await
        } else {
            crate::server::service::common::rounding(securitys.latest_price, 3).await
        };
        if securitys.pre_close.ne(&0.0) {
            last_price.change_value = securitys.latest_price - securitys.pre_close;
            last_price.change_rate = (last_price.change_value / securitys.pre_close) * 100 as f64;
            last_price.change_value = crate::server::service::common::rounding(last_price.change_value, 2).await;
            last_price.change_rate = crate::server::service::common::rounding(last_price.change_rate, 2).await;
        }
        last_price
    }

    pub async fn get_ticks(stock_codes: &Vec<String>) -> Vec<YsHqInfo> {
        let mut us = Vec::new();
        for stock_code in stock_codes.iter() {
            if let Some(s) = USSECURITY.read().await.get(stock_code) {
                us.push(s.to_owned());
            }
        }
        UsMartClient::convert_to_ys_hq_infos(&us, &stock_codes).await
    }
}
