mod app;
mod client;
mod config;
mod dataservice;
mod dataview;
// mod protofiles;
mod server;
#[cfg(test)]
mod test;
mod webserver;

// use std::net::SocketAddr;

use common::logclient::*;
use server::server::ServerHandler;
// use utility::loggings;
use crate::{config::settings::Settings, dataservice::dbsetup::DbConnection};
use protoes::phoenixaccountriskcenter::account_risk_center_server::AccountRiskCenterServer;
use tracing::*;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/accountriskcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_accountriskcenter";
    let dir = "./log";

    let settings: Settings = Settings::new().expect("init configurtion error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    // 1. 日志中心客户端初始化
    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;

    // init_logclient(&settings.servers.logcenterserver, "phoenix_accountriskcenter").await;

    let dbconn = DbConnection::new(&&settings.database.stock_uri.as_str()).await;

    let server = prepare(&settings, &dbconn).await.expect("Init server error......");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings, &dbconn).await
}

async fn prepare(settings: &Settings, dbconn: &DbConnection) -> anyhow::Result<ServerHandler> {
    let grpc = ServerHandler::new(&settings, &dbconn).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings, dbconn: &DbConnection) -> Result<(), Box<dyn std::error::Error>> {
    // let addr = "0.0.0.0:60000".parse().unwrap();
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();

    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        // log_info("Ctrl-c received, shutting down......").await;
        tx.send(()).ok();
    });

    //增加http接口
    let http_url = format!("{}:{}", settings.application.apphost, settings.application.httpport);
    // let httpaddr: SocketAddr = http_url.as_str().parse().expect("parse httpaddr error");
    let app = webserver::routers::create_route(dbconn);

    let listener = tokio::net::TcpListener::bind(&http_url).await.unwrap();

    // info!("Starting accountriskcenter http server on {:?}", &httpaddr);
    // log_debug(&format!("Starting assetscenter http server on {:?}", &httpaddr)).await;
    // log_info(&format!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"))).await;
    tokio::spawn(async move {
        axum::serve(listener, app.into_make_service()).await.unwrap();
    });

    tonic::transport::Server::builder()
        .add_service(AccountRiskCenterServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await
        .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    log_info("phoenix_accountriskcenter Shutted down").await;
    Ok(())
}
