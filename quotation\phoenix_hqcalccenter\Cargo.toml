[package]
name = "phoenix_hqcalccenter"
version = "0.2.0"
edition = "2021"
description = "行情计算中心 build time: 2025-06-05"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
utility = { workspace = true }
akaclient = { workspace = true }
common = { workspace = true }
protoes = { workspace = true }
# async-stream = { workspace = true }
futures-util = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tonic = { workspace = true }
futures = { workspace = true }
futures-lite = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
tracing = { workspace = true }
scylla = { workspace = true }
#redis
# redis = { version = "0.21", features = ["tokio-comp", "cluster", "r2d2"] }
# redis_cluster_async = "0.7"
# mobc-redis = "0.7.0"
# mobc = "0.7.3"
# 序列化
serde = { workspace = true }
serde_json = { workspace = true }
# serde_yaml = { workspace = true }
# rand = "0.8"
# Required for wellknown types
dashmap = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }
anyhow = { workspace = true }
# thiserror = "1.0"
config = { workspace = true }
# log4rs = "1.1"
sled = { workspace = true }
#time
chrono = { workspace = true }
time = { workspace = true }
lazy_static = { workspace = true }
reqwest = { workspace = true }
# gRPC
[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true
# protoc-rust = "2.25.1"
# [[bin]]
# name = "test"
# path = "src/test.rs"

[[bin]]
name = "repairprogram"
path = "src/repairprogram.rs"


[[bin]]
name = "cleanupdata"
path = "src/cleanupdata.rs"

[[bin]]
name = "phoenix_hqcalccenter"
path = "src/main.rs"

[[bin]]
name = "phoenix_hqcalccenterHk"
path = "src/main.rs"
