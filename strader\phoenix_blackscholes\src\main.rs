#[macro_use]
extern crate anyhow;
extern crate chrono;

mod bscommon;
mod config;
mod dataservice;
mod protofiles;
mod server;
#[cfg(test)]
mod tests;
mod webserver;

use crate::config::settings::Settings;
use crate::dataservice::dbsetup::DbConnection;
use crate::protofiles::blackscholes::phoenix_blackscholes_server::PhoenixBlackscholesServer;
use crate::server::client::hq_client::HqCenterClient;
use crate::server::server::BlackscholesHandler;
use crate::webserver::routers::create_route;

use akaclient::akaclient::{AkaCacheOption, AkaClient};
use anyhow::Result;
use axum::Router;
use common::logclient::*;

use server::client::managerunitclient::ManagerClient;
use std::net::SocketAddr;
use std::process;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;

#[tokio::main]
async fn main() -> Result<()> {
    // let cfg = "config/blackscholes.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_blackscholes";
    let dir = "./log";

    let settings = Settings::new().unwrap();
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);
    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", "blackscholes")).await;

    let db = Arc::new(DbConnection::new(&settings.database.stock_uri).await);
    let opt = AkaCacheOption::default();
    let aka_client = Arc::new(AkaClient::init(settings.servers.akacenterserver.to_string(), &opt).await);
    let hq_client = Arc::new(RwLock::new(HqCenterClient::new(&settings.servers.hqcenterserver).await));
    let manager_client = Arc::new(RwLock::new(ManagerClient::new(&settings.servers.managerserver).await));

    let server = BlackscholesHandler::new(&settings, aka_client.clone(), hq_client.clone(), db.clone(), manager_client.clone())
        .await
        .unwrap_or_else(|err| {
            error!("{:?} 服务启动失败...", err);
            // log_error(&format!("{} 服务启动失败...", err)).await;
            // panic!("{}", err)
            process::exit(1);
        });
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport);
    let app = create_route(db, hq_client, manager_client, aka_client, Arc::new(settings));
    info!(
        "Server started on {} name: {} version: {} description: {}",
        addr,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {} name: {} version: {} description: {}",
        addr,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;
    let _ = run_server(server, app).await.unwrap();

    Ok(())
}

async fn run_server(mut server: BlackscholesHandler, app: Router) -> Result<(), Box<dyn std::error::Error>> {
    let http_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.http_port);

    // run it with hyper
    let listener = tokio::net::TcpListener::bind(&http_url).await.unwrap();

    tokio::spawn(async move {
        info!("listening on {}", listener.local_addr().unwrap());
        axum::serve(listener, app.into_make_service_with_connect_info::<SocketAddr>()).await.unwrap();
    });

    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport);
    let addr = app_url.as_str().parse().unwrap();

    // info!("Starting blackscholes service on: {}", addr);
    // info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder() //创建可以配置[server]的新服务器生成器。
        .tcp_keepalive(Some(std::time::Duration::from_secs(60)))
        .add_service(PhoenixBlackscholesServer::new(server))
        .serve_with_shutdown(addr, async {
            //使用该服务器创建一个未来，在tokio的服务器上执行该服务器,并在接收到所提供的信号时关闭。
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
