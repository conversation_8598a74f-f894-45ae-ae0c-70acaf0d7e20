use anyhow::Result;
use futures::Stream;
use futures_util::SinkExt;
use std::sync::Arc;
use std::{pin::Pin, time::Duration};
use tokio::net::TcpSocket;
use tokio::sync::broadcast::{self, Sender};
use tokio::sync::{mpsc, oneshot};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use tokio_util::codec::Framed;
use tonic::{Request, Response, Status};
use tracing::{error, info, warn};
// use byteorder::{BigEndian, ByteOrder};

use protoes::{
    hqmsg::YsHqInfo,
    marketdata::{market_data_servers_server::MarketDataServers, CommodityMsg, ContractMsg, QryContractMsg},
};
use utility::timeutil::{build_naive_time, current_naive_time, today_weekend};

// use crate::dataservice::entities::prelude::SysCommodityTmp;
// use crate::client::DbClient;
use super::controller::FiuHqController;
use crate::codec::{BtType, FiuHeader, FiuType, HkHqCodec, HsHqCodec, MsgHeader};
use crate::common::{convert_cnstocks_to_tick, convert_pb_snapshot_to_tick, insert_cn_order, insert_pb_order, SecurityType};
use crate::config::settings::Settings;
use crate::fiuhq::{Exec, HqDataSource};
use crate::protofiles::{CnOrder, CnStock, PbOrder, PbSnapshot};

type YsHqStream = Pin<Box<dyn Stream<Item = Result<YsHqInfo, Status>> + Send>>;
type QryStream = Pin<Box<dyn Stream<Item = Result<QryContractMsg, Status>> + Send>>;

type StubType = Arc<FiuHqController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    #[allow(dead_code)]
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

#[allow(dead_code)]
pub struct FiuHqServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
    tx_tick: broadcast::Sender<YsHqInfo>,
}

impl FiuHqServerHandler {
    #[allow(dead_code)]
    pub async fn new(setting: &Settings) -> FiuHqServerHandler {
        let (tx, mut rx) = mpsc::channel(32);
        let (tx_close, mut rx_close) = oneshot::channel();

        let (tx_tick, mut rx_tick) = broadcast::channel::<YsHqInfo>(1024);

        let (tx_stock, mut rx_stock) = broadcast::channel::<CnStock>(1024);
        let (tx_cn_order, mut rx_cn_order) = broadcast::channel::<CnOrder>(1024);
        let (tx_snapshot, mut rx_snapshot) = broadcast::channel::<PbSnapshot>(1024);
        let (tx_pb_order, mut rx_pb_order) = broadcast::channel::<PbOrder>(1024);

        let stub = FiuHqController::new(&setting).await;
        let mut stub_pull = stub.clone();
        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();
        // let spp = stub.clone();

        let mut interval = tokio::time::interval(std::time::Duration::from_secs(1));
        let pull_time = format!("{}", build_naive_time(&setting.system.pullcodetime));
        let web_tick = tx_tick.clone();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let current_time = format!("{}", current_naive_time().time().format("%H:%M:%S"));
                        if !today_weekend() && (stub_pull.pull_ex == Exec::Stop as i32 || pull_time == current_time) {
                            let _ = stub_pull.pull_code(web_tick.clone()).await;
                        }
                    }
                }
            }
        });

        let tcp = HqDataSource::new(&setting.system.hsfiusocket, &setting.system.hkfiusocket, tx_stock, tx_snapshot, tx_cn_order, tx_pb_order).await;
        info!("client info: {:#?}", tcp);
        let mut hs_hq = tcp.clone();
        let mut hk_hq = tcp.clone();
        let mut hs_interval = tokio::time::interval(std::time::Duration::from_secs(1));
        let mut hk_interval = tokio::time::interval(std::time::Duration::from_secs(1));
        let hs_restart_time = format!("{}", build_naive_time(&setting.system.hsrestarttime));
        let hk_restart_time = format!("{}", build_naive_time(&setting.system.hkrestarttime));

        tokio::spawn(async move {
            // hs_interval.tick().await; //等待相应时间后执行
            loop {
                tokio::select! {
                    _ = hs_interval.tick() => {
                        let current_time = format!("{}", current_naive_time().time().format("%H:%M:%S"));
                        if hs_hq.hs_ex == Exec::Stop as i32 || hs_restart_time == current_time {
                            let _ = hs_hq.hs_hq_process().await;
                        }
                    }
                }
            }
        });
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = hk_interval.tick() => {
                        let current_time = format!("{}", current_naive_time().time().format("%H:%M:%S"));
                        if hk_hq.hk_ex == Exec::Stop as i32 || hk_restart_time == current_time{
                            let _ = hk_hq.hk_hq_process().await;
                        }
                    }
                }
            }
        });

        let ret = FiuHqServerHandler {
            stub,
            settings: setting.clone(),
            task_dispacther: tx,
            set_close: Some(tx_close),
            tx_tick: tx_tick.clone(),
        };

        let tx_cn = tx_tick.clone();
        let tx_pb = tx_tick.clone();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = rx_stock.recv() => {
                        if let Ok(hs_tick) = task {
                            if hs_tick.r#type == SecurityType::Stock as i32 || hs_tick.r#type == SecurityType::Fund as i32
                            /*|| hs_tick.r#type == SecurityType::Index as i32 */|| hs_tick.r#type == SecurityType::BShare as i32 {
                                crate::common::debug_display(&hs_tick).await;
                                if let Some(tick) = convert_cnstocks_to_tick(&hs_tick).await {
                                    let _ = tx_cn.send(tick);
                                }
                            }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = rx_snapshot.recv() => {
                        if let Ok(hk_tick) = task {
                            crate::common::debug_display(&hk_tick).await;
                            if let Some(tick) = convert_pb_snapshot_to_tick(&hk_tick).await {
                                let _ = tx_pb.send(tick);
                            }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = rx_cn_order.recv() => {
                        if let Ok(hs_order) = task {
                            if hs_order.r#type == SecurityType::Stock as i32 || hs_order.r#type == SecurityType::Fund as i32
                            /*|| hs_order.r#type == SecurityType::Index as i32*/ || hs_order.r#type == SecurityType::BShare as i32 {
                                crate::common::debug_display(&hs_order).await;
                                insert_cn_order(&hs_order).await;
                                // if hs_order.symbol == "600000" {
                                    // info!("{:?}", hs_order);
                                // }
                            }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = rx_pb_order.recv() => {
                        if let Ok(hk_order) = task {
                            crate::common::debug_display(&hk_order).await;
                            insert_pb_order(&hk_order).await;
                            // if hk_order.code == 03690 {
                            //     info!("{:?}", hk_order);
                            // }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {
                        info!("task from client received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        task(stub_for_dispatch.clone()).await;
                    }
                    tick_task = rx_tick.recv() => {
                        //do nothing......
                        if let Ok(tick) = tick_task {
                            if tick.contract_no1 == "600000_XSHG" || tick.contract_no1 == "03690_XHKG" || tick.contract_no1 == "000001_XSHE" || tick.contract_no1 == "000001_XSHG" ||
                                tick.contract_no1 == "HY020604_HK" || tick.contract_no1 == "HY110100_HS" {
                                // info!("exchange:{} code:{} time:{:?} open:{} close:{} high:{} low:{} last:{} preclose:{} changevalue:{} changerate:{} qty:{} turnover:{} avg:{}",
                                // tick.exchange_id, tick.contract_no1, tick.tapidtstamp, tick.q_opening_price, tick.q_closing_price, tick.q_high_price, tick.q_low_price, tick.q_last_price,
                                // tick.q_pre_closing_price, tick.q_change_value, tick.q_change_rate, tick.q_total_qty, tick.q_total_turnover, tick.q_average_price);
                                info!("exchange:{} code:{} time:{:?}", tick.exchange_id, tick.contract_no1, tick.tapidtstamp);
                            }
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        // tokio::spawn(async move {
        //     loop {
        //         tokio::time::sleep(Duration::from_secs(30)).await;
        //         info!("start pull code====================");
        //         let s = spp.fiu_api.post_hs_hq().await;
        //         for v in s.iter() {
        //             if v.symbol.is_empty() {
        //                 continue;
        //             }
        //             let t = crate::common::convert_tick(v.clone()).await;
        //             tx_tick.clone().send(t).unwrap();
        //         }
        //     }
        // });

        ret
    }
    #[allow(dead_code)]
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl MarketDataServers for FiuHqServerHandler {
    type SubscribeMarketDataStream = YsHqStream;
    async fn subscribe_market_data(&self, request: Request<ContractMsg>) -> Result<Response<Self::SubscribeMarketDataStream>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());

        let (tx, rx) = mpsc::channel(204800);

        let mut tick_clone = self.tx_tick.subscribe();
        tokio::spawn(async move {
            loop {
                tokio::select! {
                    task = tick_clone.recv() => {
                        if let Ok(tick) = task {
                            // info!("received ticks code: {},  time: {}", &tick.contract_no1, &tick.tapidtstamp);
                            let ret = tx.send(Result::<YsHqInfo, Status>::Ok(tick.to_owned())).await;
                            if ret.as_ref().is_err() {
                                error!("send error:{:?}", ret);
                                break;
                            }
                        }
                    }
                }
            }
        });

        let out_stream = ReceiverStream::new(rx);

        Ok(Response::new(Box::pin(out_stream) as Self::SubscribeMarketDataStream))
    }

    type QueryCommodityDataStream = QryStream;
    async fn query_commodity_data(&self, _request: Request<CommodityMsg>) -> Result<Response<Self::QueryCommodityDataStream>, tonic::Status> {
        // creating infinite stream with requested message
        let repeat = std::iter::repeat(QryContractMsg::default());
        let mut stream = Box::pin(tokio_stream::iter(repeat).throttle(Duration::from_millis(200)));

        // spawn and channel are required if you want handle "disconnect" functionality
        // the `out_stream` will not be polled after client disconnect
        let (tx, rx) = mpsc::channel(128);
        tokio::spawn(async move {
            while let Some(item) = stream.next().await {
                match tx.send(Result::<_, Status>::Ok(item)).await {
                    Ok(_) => {
                        // item (server response) was queued to be send to client
                    }
                    Err(_item) => {
                        // output_stream was build from rx and both are dropped
                        break;
                    }
                }
            }
        });

        let output_stream = ReceiverStream::new(rx);

        Ok(Response::new(Box::pin(output_stream) as Self::QueryCommodityDataStream))
    }
}

#[allow(dead_code)]
pub async fn hs_hq_process(uri: &str, _tx: Sender<CnStock>) -> Result<()> {
    let socket = TcpSocket::new_v4().unwrap();
    let addr = uri.parse().unwrap();
    let tcp_stream = socket.connect(addr).await.unwrap();
    let mut lines = Framed::new(tcp_stream, HsHqCodec);

    let mut ping_task = tokio::time::interval(Duration::from_secs(15));

    tokio::spawn(async move {
        loop {
            tokio::select! {
                //读取服务端的消息
                recv_result = lines.next() => match recv_result {
                    Some(Ok(_cn_stock)) => {
                        // info!("{:?}", msg);
                        // match msg {
                        //     FiuDateSource::Stock(cn_stock) => {
                                // if cn_stock.r#type == SecurityType::Stock as i32 || cn_stock.r#type == SecurityType::Fund as i32
                                // || cn_stock.r#type == SecurityType::Index as i32 || cn_stock.r#type == SecurityType::BShare as i32 {
                                //     // let _ = tx.send(cn_stock);
                                //     if cn_stock.symbol == "600000" {
                                //         info!("{:?}", cn_stock);
                                //     }
                                // }
                        //     },
                        //     FiuDateSource::Order(cn_order) => {
                        //         if cn_order.r#type == SecurityType::Stock as i32 || cn_order.r#type == SecurityType::Fund as i32
                        //         || cn_order.r#type == SecurityType::Index as i32 || cn_order.r#type == SecurityType::BShare as i32 {
                        //             // let _ = tx.send(cn_stock);
                        //             if cn_order.symbol == "600000" {
                        //                 info!("{:?}", cn_order);
                        //             }
                        //         }
                        //     }
                        // }
                    }
                    Some(Err(e)) => {
                        info!("error = {:?}", e);
                        continue;
                    }
                    None => {
                        info!("{} 退出", addr);
                        break
                    },
                },
                _ = ping_task.tick() => {//心跳
                    let heartbeat = MsgHeader {
                        us_length: 3,
                        bt_type: BtType::ZlClientHeartbeat as u8,
                    };
                    info!("send heartbeat");
                    lines.send(heartbeat).await.unwrap();
                }
            }
        }
    });

    Ok(())
}

#[allow(dead_code)]
pub async fn hk_hq_process(uri: &str, _tx: Sender<PbSnapshot>) -> Result<()> {
    let socket = TcpSocket::new_v4().unwrap();
    let addr = uri.parse().unwrap();
    let tcp_stream = socket.connect(addr).await.unwrap();
    let mut lines = Framed::new(tcp_stream, HkHqCodec);

    let mut ping_task = tokio::time::interval(Duration::from_secs(15));

    tokio::spawn(async move {
        loop {
            tokio::select! {
                //读取服务端的消息
                recv_result = lines.next() => match recv_result {
                    Some(Ok(msg)) => {
                        info!("{:?}", msg);
                    }
                    Some(Err(e)) => {
                        info!("error = {:?}", e);
                        continue;
                    }
                    None => {
                        info!("{} 退出", addr);
                        break
                    },
                },
                _ = ping_task.tick() => {//心跳
                    let heartbeat = FiuHeader {
                        us_len: 3,
                        c_type: FiuType::FiuHeartbeat as u8,
                    };
                    lines.send(heartbeat).await.unwrap();
                }
            }
        }
    });

    Ok(())
}
