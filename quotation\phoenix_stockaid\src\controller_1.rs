use crate::config::settings::Settings;
use anyhow::Result;
use common::logclient::LogClient;

use std::collections::{BTreeMap, HashMap};

use crate::client::hqclient::HqCenterClient;
use crate::dataservice::dbsetup::DbConnection;
use messagecenter::protofiles::hqmsg::YsHqInfo;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::protofiles;
use crate::protofiles::hqcenter::TickHqReq;
use crate::protofiles::phoenixstockaid::{DetailCodeInfo, DetailPlateInfo, ReqDetailMsg, ReqIndexOrRankMsg, ReqTrsMsg, ResultMainIndexMsg, ResultMainRankMsg, ResultPlateInfoMsg};
use crate::service::stockdb;
use tracing::{error, info};

//板块信息
#[derive(Debug, Clone)]
pub struct MainPlateInfo {
    pub plate_id: i32,
    pub plate_code: String,
    pub plate_name: String,
}
// enum MarketType
// {
// 	MT_HK = 1,//香港
// 	MT_US = 2,//美股
// 	MT_CN = 3,//沪深
// }
// #[derive(Clone)]
#[allow(dead_code)]
pub struct ServerController {
    pub settings: Arc<RwLock<Settings>>,
    pub plate_type: Arc<RwLock<HashMap<String, i32>>>,               //主板 板块属性  板块stock_code, 板块类型 '1：自定义板块，2：固定板块, 3:行业板块',
    pub market_plate: Arc<RwLock<HashMap<i32, Vec<MainPlateInfo>>>>, //区域主页板块  分市场区域板块汇总, 市场类型,板块信息

    pub plate_stock: Arc<RwLock<HashMap<i32, Vec<String>>>>,       //板块内所有代码   // 板块id ,股票所有code
    pub market_index: Arc<RwLock<HashMap<i32, Vec<String>>>>,      //区域主页指数 市场指数
    pub market_stock: Arc<RwLock<HashMap<i32, Vec<String>>>>,      //key:市场类型  1：港股，2：美股，3：沪深' ,市场所有股票代码
    pub market_plate_info: Arc<RwLock<HashMap<i32, Vec<String>>>>, //市场所有板块代码

    pub stock_belong_plate: Arc<RwLock<HashMap<String, Vec<i32>>>>, //股票所属板块信息   板块id

    pub stock_belong_market: Arc<RwLock<HashMap<String, i32>>>, //股票所属市场信息

    pub stock_tick: Arc<RwLock<HashMap<String, YsHqInfo>>>, //所有股票行情

    pub all_stock_info: Arc<RwLock<Vec<String>>>, //所有股票信息副本,轮询用

    pub stock_extent_info: Arc<RwLock<BTreeMap<i32, Vec<String>>>>, //所有排序  涨跌幅

    pub stock_extent_info_belong_plate: Arc<RwLock<HashMap<i32, BTreeMap<i32, Vec<String>>>>>, //每单个板块里面排序 涨跌幅  key:板块id

    pub plate_name_info: Arc<RwLock<HashMap<i32, HashMap<String, MainPlateInfo>>>>, //板块信息关联

    pub finances_db: Arc<DbConnection>, //数据库连接
    pub hq_center_client: HqCenterClient,

    //TRS 代码
    pub trs_stock_info: Arc<RwLock<Vec<String>>>, //trs股票代码

    pub plate_cache: Arc<RwLock<HashMap<String, YsHqInfo>>>, //所有股票信息
}

//处理业务逻辑
impl ServerController {
    pub async fn init_controller(&mut self) -> Result<()> {
        //查询所有品种组分类表
        let trade_list = stockdb::query_sys_trade_config_commodity(&self.finances_db).await;
        if trade_list.len() == 0 {
            error!("初始化查询失败");
            if let Ok(c) = LogClient::get() {
                c.push_error(&format!("初始化查询失败")).await;
            }
            return Ok(());
        }
        for val in trade_list.iter() {
            let ret = stockdb::query_inner_code(&self.finances_db, val.commodity_id).await;
            if let Ok(stock_info) = ret {
                self.trs_stock_info.write().await.push(stock_info.inner_code.clone());
                self.stock_tick.write().await.insert(stock_info.inner_code.clone(), YsHqInfo::default());
            }
        }
        //查询指数
        if let Ok(index_list) = stockdb::query_index(&self.finances_db).await {
            // info!("{:?}", index_list);
            for val in index_list.iter() {
                self.all_stock_info.write().await.push(val.inner_code.clone());
                self.stock_tick.write().await.insert(val.inner_code.clone(), YsHqInfo::default());
                let mut market_index = self.market_index.write().await;
                match market_index.get_mut(&val.r#type) {
                    Some(sl) => sl.push(val.inner_code.clone()),
                    None => {
                        let mut code_list: Vec<String> = Vec::new();
                        code_list.push(val.inner_code.clone());
                        market_index.insert(val.r#type, code_list);
                    }
                }
            }
        } else {
            error!("query_index error");
        }

        //多表联查 确定板块内有哪些股票代码
        if let Ok(plate_list) = stockdb::query_plate(&self.finances_db).await {
            // info!("{:?}", plate_list);
            let mut plate_type = self.plate_type.write().await;
            let mut market_plate = self.market_plate.write().await;
            let mut plate_name_info = self.plate_name_info.write().await;
            let mut plate_stock = self.plate_stock.write().await;
            let mut market_stock = self.market_stock.write().await;
            let mut stock_belong_plate = self.stock_belong_plate.write().await;
            let mut stock_belong_market = self.stock_belong_market.write().await;
            let mut market_plate_info = self.market_plate_info.write().await;

            for val in plate_list.iter() {
                let mp = MainPlateInfo {
                    plate_id: val.id as i32,
                    plate_code: val.inner_code.clone(),
                    plate_name: val.name.clone(),
                };

                if !plate_type.contains_key(&val.inner_code) {
                    plate_type.insert(val.inner_code.clone(), val.cate);
                    if val.cate == 1 || val.cate == 2 {
                        match market_plate.get_mut(&val.market_type) {
                            Some(pi) => {
                                pi.push(mp.clone());
                            }
                            None => {
                                let mut p_list = Vec::new();
                                p_list.push(mp.clone());
                                market_plate.insert(val.market_type, p_list);
                            }
                        }
                    }
                }
                match plate_name_info.get_mut(&val.market_type) {
                    Some(psi) => match psi.get(val.inner_code.clone().as_str()) {
                        None => {
                            psi.insert(val.inner_code.clone(), mp.clone());
                        }
                        Some(_) => {}
                    },
                    None => {
                        let mut hm = HashMap::new();
                        hm.insert(val.inner_code.clone(), mp.clone());
                        plate_name_info.insert(val.market_type, hm);
                    }
                }

                match plate_stock.get_mut(&(val.id as i32)) {
                    Some(psi) => {
                        psi.push(val.stock_code.clone());
                        match market_stock.get_mut(&val.market_type) {
                            Some(m_val) => m_val.push(val.inner_code.clone()),
                            None => {}
                        }
                    }
                    None => {
                        let mut s_list = Vec::new();
                        s_list.push(val.stock_code.clone());
                        plate_stock.insert(val.id as i32, s_list);
                        match market_stock.get_mut(&val.market_type) {
                            Some(sil) => {
                                sil.push(val.inner_code.clone());
                            }
                            None => {
                                let mut s_list = Vec::new();
                                s_list.push(val.inner_code.clone());
                                market_stock.insert(val.market_type, s_list);
                            }
                        }

                        let index = self.all_stock_info.read().await.iter().position(|x| x.to_string() == val.inner_code.clone());
                        match index {
                            None => self.all_stock_info.write().await.push(val.inner_code.clone()),
                            Some(_) => {}
                        }
                    }
                }

                match stock_belong_plate.get_mut(&val.stock_code) {
                    Some(sl) => {
                        sl.push(val.id as i32);
                    }
                    None => {
                        let mut s_list = Vec::new();
                        s_list.push(val.id as i32);
                        stock_belong_plate.insert(val.stock_code.clone(), s_list);
                        self.all_stock_info.write().await.push(val.stock_code.clone());
                    }
                }

                stock_belong_market.insert(val.stock_code.clone(), val.market_type);
                stock_belong_market.insert(val.inner_code.clone(), val.market_type);
                match market_plate_info.get_mut(&val.market_type) {
                    None => {
                        let mut pl = Vec::new();
                        pl.push(val.inner_code.clone());
                        market_plate_info.insert(val.market_type, pl);
                    }
                    Some(p_l) => {
                        if let Some(_index) = p_l.iter().position(|x| x == val.inner_code.clone().as_str()) {
                        } else {
                            p_l.push(val.inner_code.clone());
                        }
                    }
                }
            }
        } else {
            error!("query_plate error");
        }

        let mut stock_belong_plate = self.stock_belong_plate.write().await;
        let mut stock_extent_info_belong_plate = self.stock_extent_info_belong_plate.write().await;
        let mut count = self.all_stock_info.read().await.len();
        let mut req = TickHqReq {
            strcontractno: "".to_string(),
            iticktype: 0,
            ticktime: 0,
            realtime: 0,
        };
        for stock_val in self.all_stock_info.read().await.iter() {
            match stock_belong_plate.get_mut(&*stock_val.clone()) {
                Some(sl) => {
                    for val in sl.iter() {
                        match stock_extent_info_belong_plate.get_mut(val) {
                            Some(sp) => {
                                //  let  mut list= sp.c;
                                let em = 0;
                                match sp.get_mut(&em) {
                                    None => {
                                        let mut new_list = Vec::new();
                                        new_list.push(stock_val.clone());
                                        sp.insert(0, new_list);
                                    }
                                    Some(btree_list) => {
                                        btree_list.push(stock_val.clone());
                                    }
                                }
                            }
                            None => {
                                // info!("not found in stock_extent_info_belong_plate");
                                let mut bt_map = BTreeMap::new();
                                let mut bt_list = Vec::new();
                                bt_list.push(stock_val.clone());
                                bt_map.insert(0, bt_list);
                                stock_extent_info_belong_plate.insert(*val, bt_map.clone());
                            }
                        }
                    }
                }
                None => {}
            }
        }

        for stock_val in self.trs_stock_info.read().await.iter() {
            match stock_belong_plate.get_mut(&*stock_val.clone()) {
                Some(sl) => {
                    for val in sl.iter() {
                        match stock_extent_info_belong_plate.get_mut(val) {
                            Some(sp) => {
                                let em = 0;
                                match sp.get_mut(&em) {
                                    None => {
                                        let mut new_list = Vec::new();
                                        new_list.push(stock_val.clone());
                                        sp.insert(0, new_list);
                                    }
                                    Some(btree_list) => {
                                        if let Some(_index) = btree_list.iter().position(|x| x == &stock_val.clone()) {
                                            continue;
                                        } else {
                                            btree_list.push(stock_val.clone());
                                        }
                                    }
                                }
                            }
                            None => {
                                // info!("not found in stock_extent_info_belong_plate");
                                let mut bt_map = BTreeMap::new();
                                let mut bt_list = Vec::new();
                                bt_list.push(stock_val.clone());
                                bt_map.insert(0, bt_list);
                                stock_extent_info_belong_plate.insert(*val, bt_map.clone());
                            }
                        }
                    }
                }
                None => {}
            }
        }

        drop(stock_extent_info_belong_plate);
        drop(stock_belong_plate);
        let plate_type = self.plate_type.read().await;
        for val in self.all_stock_info.read().await.iter() {
            // info!("{:?}", val);
            if count % 50 == 0 && count != self.all_stock_info.read().await.len() && count != 0 {
                // info!("=================");
                req.strcontractno = req.strcontractno[0..req.strcontractno.len() - 1].to_string();
                let mut c = self.hq_center_client.clone();
                let _ = self.get_hq(&req, &mut c).await;
                req.strcontractno = "".to_string();
                count -= 1;
            } else {
                count -= 1;
                if plate_type.contains_key(val) {
                    if let Some(pt) = plate_type.get(val) {
                        if *pt == 1 {
                            continue;
                        }
                    }
                }
                req.strcontractno = format!("{}{},", req.strcontractno, val);
                if count == 0 {
                    // info!("1=================");
                    req.strcontractno = req.strcontractno[0..req.strcontractno.len() - 1].to_string();
                    let mut c = self.hq_center_client.clone();
                    let _ = self.get_hq(&req, &mut c).await;
                    break;
                }
            }
        }
        let mut count = self.trs_stock_info.read().await.len();
        let mut req = TickHqReq {
            strcontractno: "".to_string(),
            iticktype: 0,
            ticktime: 0,
            realtime: 0,
        };
        for val in self.trs_stock_info.read().await.iter() {
            // info!("{:?}", val);
            if count % 50 == 0 && count != self.trs_stock_info.read().await.len() && count != 0 {
                // info!("2=================");
                req.strcontractno = req.strcontractno[0..req.strcontractno.len() - 1].to_string();
                let mut c = self.hq_center_client.clone();
                let _ = self.get_hq(&req, &mut c).await;
                req.strcontractno = "".to_string();
                count -= 1;
            } else {
                count -= 1;
                if plate_type.contains_key(val) {
                    if let Some(pt) = plate_type.get(val) {
                        if *pt == 1 {
                            continue;
                        }
                    }
                }
                req.strcontractno = format!("{}{},", req.strcontractno, val);
                if count == 0 {
                    // info!("3=================");
                    req.strcontractno = req.strcontractno[0..req.strcontractno.len() - 1].to_string();
                    let mut c = self.hq_center_client.clone();
                    let _ = self.get_hq(&req, &mut c).await;

                    break;
                }
            }
        }
        info!("{:?}", self.plate_type);
        info!("{:?}", self.market_plate_info);
        Ok(())
    }

    pub async fn get_hq(&self, req: &TickHqReq, hq_client: &mut HqCenterClient) -> Result<()> {
        info!("{:?}", req);
        let mut stock_tick = self.stock_tick.write().await;
        if let Ok(ret_val) = hq_client.post_tick_hq(&req).await {
            if ret_val.tick_hq_info.len() != 0 {
                for hq_info in ret_val.tick_hq_info.iter() {
                    let hq_copy: YsHqInfo;
                    let hq = self.return_hq(&hq_info).await;
                    let code = hq_info.contract_no1.clone();
                    let mut flag_temp = false;
                    match stock_tick.get_mut(&*hq_info.contract_no1.clone()) {
                        None => {
                            stock_tick.insert(hq_info.contract_no1.clone(), hq.clone());
                            hq_copy = hq.clone();
                            flag_temp = true;
                        }
                        Some(hq_val) => {
                            hq_copy = hq_val.clone();
                            *hq_val = hq.clone();
                        }
                    }
                    let hq_info = (hq.q_change_rate * 100.0) as i32;
                    let hq_copy = (hq_copy.q_change_rate * 100.0) as i32;
                    if flag_temp {
                        let _ = self.deal_hq(&code, hq_info, 0).await;
                        let _ = self.deal_all_stock(&code, hq_info, 0).await;
                    } else {
                        let _ = self.deal_hq(&code, hq_info, hq_copy).await;
                        let _ = self.deal_all_stock(&code, hq_info, hq_copy).await;
                    }
                    let _ = self.deal_cache(&hq).await;
                }
            } else {
                // 用redis去查询
                let str_list: Vec<&str> = req.strcontractno.split(",").collect();
                for val in str_list.iter() {
                    let req = TickHqReq {
                        strcontractno: val.to_string().clone(),
                        iticktype: 0,
                        ticktime: 0,
                        realtime: 0,
                    };
                    let ret = hq_client.post_tick_hq(&req).await;
                    if let Ok(ret_val) = ret {
                        if ret_val.tick_hq_info.len() != 0 {
                            let hq_info = ret_val.tick_hq_info[0].clone();
                            let hq_copy: YsHqInfo;
                            let mut flag_temp = false;
                            let hq = self.return_hq(&hq_info).await;
                            let code = hq_info.contract_no1.clone();
                            match stock_tick.get_mut(&*hq_info.contract_no1.clone()) {
                                None => {
                                    stock_tick.insert(hq_info.contract_no1.clone(), hq.clone());
                                    hq_copy = hq.clone();
                                    flag_temp = true;
                                }
                                Some(hq_val) => {
                                    hq_copy = hq_val.clone();
                                    *hq_val = hq.clone();
                                }
                            }
                            let hq_info = (hq.q_change_rate * 100.0) as i32;
                            let hq_copy = (hq_copy.q_change_rate * 100.0) as i32;
                            if flag_temp {
                                let _ = self.deal_hq(&code, hq_info, 0).await;
                                let _ = self.deal_all_stock(&code, hq_info, 0).await;
                            } else {
                                let _ = self.deal_hq(&code, hq_info, hq_copy).await;
                                let _ = self.deal_all_stock(&code, hq_info, hq_copy).await;
                            }

                            let _ = self.deal_cache(&hq).await;
                        }
                    }
                }
            }
        } else {
            info!("没有查询到hq信息 stock: {:?}", req.strcontractno);
        }
        //
        Ok(())
    }

    pub async fn update_us(&self) -> Result<Vec<String>> {
        let mut list = Vec::new();
        match self.market_plate.read().await.get(&2) {
            None => {
                info!("not fount market_plate");
            }
            Some(plate) => {
                // info!("{:?}", plate);
                for m_p_info in plate.iter() {
                    let pl_info = DetailPlateInfo {
                        id: m_p_info.plate_id,
                        code: m_p_info.plate_code.clone(),
                        name: m_p_info.plate_name.clone(),
                        count: 5,
                        stock_infos: vec![],
                    };
                    if m_p_info.plate_code == "ZG0001_US".to_string() {
                        continue;
                    }
                    match self.plate_stock.read().await.get(&pl_info.id) {
                        None => {
                            info!("信息不完整");
                        }
                        Some(stock_list) => {
                            for value in stock_list.iter() {
                                list.push(value.to_string());
                            }
                        }
                    }
                }
            }
        }
        Ok(list)
    }

    pub async fn deal_hq(&self, code: &String, hq_info: i32, hq_copy: i32) -> Result<()> {
        let stock_code = code.clone();
        let mut stock_extent_info_belong_plate = self.stock_extent_info_belong_plate.write().await;
        match self.stock_belong_plate.read().await.get(&*stock_code.clone()) {
            Some(sl) => {
                for val in sl.iter() {
                    match stock_extent_info_belong_plate.get_mut(val) {
                        Some(sp) => {
                            //  let  mut list= sp.c;
                            // let mut em = (hq_copy.q_change_rate * 100.0) as i32;
                            match sp.get_mut(&hq_copy) {
                                None => {
                                    //                                       info!("not found in btree map");
                                }
                                Some(btree_list) => {
                                    if let Some(index) = btree_list.iter().position(|x| x == &stock_code.clone()) {
                                        btree_list.remove(index);
                                    }
                                }
                            }

                            match sp.get_mut(&hq_info) {
                                None => {
                                    //                                       info!("not found in btree map");
                                    let mut new_list = Vec::new();
                                    new_list.push(stock_code.clone());
                                    sp.insert(hq_info, new_list);
                                }
                                Some(btree_list) => {
                                    btree_list.push(stock_code.clone());
                                    //            info!("{:?}",btree_list);
                                }
                            }
                        }
                        None => {
                            // info!("not found in stock_extent_info_belong_plate");
                            let mut bt_map = BTreeMap::new();
                            let mut bt_list = Vec::new();
                            bt_list.push(stock_code.clone());
                            bt_map.insert(hq_info, bt_list);
                            stock_extent_info_belong_plate.insert(*val, bt_map.clone());
                        }
                    }
                }
            }
            None => {
                //flag = true;
                // info!("not found in stock_belong_plate :{:?}",stock_code.clone());
            }
        }
        Ok(())
    }

    pub async fn deal_all_stock(&self, code: &String, hq_info: i32, hq_copy: i32) -> Result<()> {
        let stock_code = code.clone();
        let mut stock_extent_info = self.stock_extent_info.write().await;
        match stock_extent_info.get_mut(&hq_copy) {
            None => {
                //                info!("not found in btree map");
            }
            Some(btree_list) => {
                if let Some(index) = btree_list.iter().position(|x| x == &stock_code.clone()) {
                    //                    info!("del index:{:?},code:{:?}",index,stock_code);
                    btree_list.remove(index);
                }
            }
        }
        let mut hq_flag = false;
        match stock_extent_info.get_mut(&hq_info) {
            None => {
                hq_flag = true;
            }
            Some(btree_list) => {
                btree_list.push(stock_code.clone());
                //             info!("{:?}",btree_list);
            }
        }
        if hq_flag {
            let mut new_list = Vec::new();
            new_list.push(stock_code.clone());
            stock_extent_info.insert(hq_info, new_list);
        }
        Ok(())
    }

    pub async fn deal_cache(&self, hq_info: &YsHqInfo) -> Result<()> {
        let stock_code = hq_info.contract_no1.clone();
        match self.plate_type.read().await.get(&stock_code) {
            None => {}
            Some(_) => {
                self.plate_cache.write().await.insert(stock_code, hq_info.clone());
            }
        }
        Ok(())
    }

    pub async fn post_main_index_msg(&self, in_stream: ReqIndexOrRankMsg) -> Result<ResultMainIndexMsg> {
        let mut res = ResultMainIndexMsg { ..Default::default() };

        match self.market_index.read().await.get(&in_stream.market_type) {
            None => {
                info!("not found index info ")
            }
            Some(val) => {
                for index_code in val.iter() {
                    // info!("{:?}", index_code);
                    match self.stock_tick.read().await.get(index_code) {
                        None => {
                            info!("not found index info ")
                        }
                        Some(val) => {
                            // info!("{:?}", val);
                            let mut ci = DetailCodeInfo {
                                code: index_code.clone(),
                                last_price: val.q_last_price,
                                change_value: val.q_change_value,
                                change_rate: val.q_change_rate,
                                top_stock: "".to_string(),
                            };
                            if val.contract_no1.contains("") {
                                let req = TickHqReq {
                                    strcontractno: index_code.clone(),
                                    iticktype: 0,
                                    ticktime: 0,
                                    realtime: 1,
                                };
                                let mut client = self.hq_center_client.clone();
                                if let Ok(ret_val) = client.post_tick_hq(&req).await {
                                    ci.last_price = ret_val.tick_hq_info[0].q_last_price;
                                    ci.change_value = ret_val.tick_hq_info[0].q_change_value;
                                    ci.change_rate = ret_val.tick_hq_info[0].q_change_rate;
                                }
                            }

                            res.index_infos.push(ci);
                        }
                    }
                }
            }
        }

        Ok(res)
    }

    pub async fn post_main_rank_msg(&self, in_stream: ReqIndexOrRankMsg) -> Result<ResultMainRankMsg> {
        let mut res = ResultMainRankMsg { ..Default::default() };
        let num = if in_stream.count == 0 { 3 } else { in_stream.count };

        match self.market_plate_info.read().await.get(&in_stream.market_type) {
            None => {
                info!("not found market_plate info ")
            }
            Some(val) => {
                let mut p_count = num;
                let list: Vec<_> = self.stock_extent_info.read().await.iter().rev().map(|x| x.1.to_owned()).collect(); //取副本
                for all_info in list.iter() {
                    //三个热门板块
                    if p_count == 0 {
                        break;
                    }
                    for all_info_val in all_info.iter() {
                        //获取板块属性
                        //                                             info!("{:?}",p_count);
                        if p_count == 0 {
                            break;
                        }
                        match self.plate_type.read().await.get(all_info_val) {
                            None => {
                                //                             info!("not found in plate_type{:?}",all_info_val);
                            }
                            Some(pt) => {
                                //    info!("{:?}",pt);
                                if *pt == 3 {
                                    if let Some(_index) = val.iter().position(|x| x == all_info_val.clone().as_str()) {
                                        let mut pl_info = DetailCodeInfo {
                                            code: "".to_string(),
                                            last_price: 0.0,
                                            change_value: 0.0,
                                            change_rate: 0.0,
                                            top_stock: "".to_string(),
                                        };

                                        //  p_info.name = all_info_val.to_string().clone();
                                        if let Some(p_info) = self.plate_name_info.read().await.get(&in_stream.market_type) {
                                            if let Some(p_name) = p_info.get(all_info_val) {
                                                pl_info.code = p_name.plate_code.clone();
                                                if let Some(s_info_copy) = self.stock_tick.read().await.get(p_name.plate_code.clone().as_str()) {
                                                    pl_info.change_rate = s_info_copy.q_change_rate;
                                                    pl_info.last_price = s_info_copy.q_last_price;
                                                    pl_info.change_value = s_info_copy.q_change_value;
                                                }
                                                match self.stock_extent_info_belong_plate.read().await.get(&p_name.plate_id) {
                                                    None => {
                                                        info!("信息不完整");
                                                    }
                                                    Some(bt_map) => {
                                                        for value in bt_map.iter() {
                                                            for vec_val in value.1 {
                                                                if let Some(_s_info_copy) = self.stock_tick.read().await.get(vec_val) {
                                                                    pl_info.top_stock = vec_val.clone();
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        //                     info!("{:?}",pl_info);
                                        res.main_plate.push(pl_info.clone());
                                        p_count -= 1;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if in_stream.market_type != 2 {
            //涨幅榜
            let mut pl_info = DetailPlateInfo {
                id: 0,
                code: "19999".to_string(),
                name: "涨幅榜".to_string(),
                count: 0,
                stock_infos: vec![],
            };
            let mut count = 5;
            let list: Vec<_> = self.stock_extent_info.read().await.iter().rev().map(|x| x.1.to_owned()).collect();
            for st_info in list.iter() {
                if count == 0 {
                    break;
                }
                for spe_stock in st_info.iter() {
                    if count == 0 {
                        break;
                    }
                    if let Some(mk_id) = self.stock_belong_market.read().await.get(spe_stock) {
                        //                    info!("{:?}",mk_id.value());
                        if *mk_id != in_stream.market_type {
                            continue;
                        } else {
                            if let Some(pl_info_some) = self.market_plate_info.read().await.get(&in_stream.market_type) {
                                if let Some(_pl) = pl_info_some.iter().find(|x| *x == spe_stock) {
                                    info!("find:{:?}", spe_stock);
                                    continue;
                                } else {
                                    if let Some(pl_info_some) = self.market_index.read().await.get(&in_stream.market_type) {
                                        if let Some(_pl) = pl_info_some.iter().find(|x| *x == spe_stock) {
                                            info!("find:{:?}", spe_stock);
                                            continue;
                                        }
                                    }
                                    if let Some(s_info_copy) = self.stock_tick.read().await.get(spe_stock) {
                                        let s_info = DetailCodeInfo {
                                            code: spe_stock.clone(),
                                            last_price: s_info_copy.q_last_price,
                                            change_value: s_info_copy.q_change_value,
                                            change_rate: s_info_copy.q_change_rate,
                                            top_stock: "".to_string(),
                                        };
                                        pl_info.stock_infos.push(s_info);
                                        count -= 1;
                                        if count == 0 {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            res.hot_plate.push(pl_info);
            //跌幅榜
            let mut pl_info = DetailPlateInfo {
                id: 0,
                code: "29999".to_string(),
                name: "跌幅榜".to_string(),
                count: 0,
                stock_infos: vec![],
            };
            count = 5;
            let list: Vec<_> = self.stock_extent_info.read().await.iter().map(|x| x.1.to_owned()).collect();
            for st_info in list.iter() {
                if count == 0 {
                    break;
                }
                for spe_stock in st_info.iter() {
                    if count == 0 {
                        break;
                    }
                    if let Some(mk_id) = self.stock_belong_market.read().await.get(spe_stock) {
                        if *mk_id != in_stream.market_type {
                            continue;
                        } else {
                            if let Some(pl_info_some) = self.market_plate_info.read().await.get(&in_stream.market_type) {
                                if let Some(_pl) = pl_info_some.iter().find(|x| *x == spe_stock) {
                                    info!("find:{:?}", spe_stock);
                                    continue;
                                } else {
                                    // info!("not find");
                                    if let Some(pl_info_some) = self.market_index.read().await.get(&in_stream.market_type) {
                                        if let Some(_pl) = pl_info_some.iter().find(|x| *x == spe_stock) {
                                            info!("find:{:?}", spe_stock);
                                            continue;
                                        }
                                    }
                                    if let Some(s_info_copy) = self.stock_tick.read().await.get(spe_stock) {
                                        let s_info = DetailCodeInfo {
                                            code: spe_stock.clone(),
                                            last_price: s_info_copy.q_last_price,
                                            change_value: s_info_copy.q_change_value,
                                            change_rate: s_info_copy.q_change_rate,
                                            top_stock: "".to_string(),
                                        };
                                        pl_info.stock_infos.push(s_info);
                                        count -= 1;
                                        if count == 0 {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            res.hot_plate.push(pl_info);
            match self.market_plate.read().await.get(&in_stream.market_type) {
                None => {
                    info!("not fount market_plate");
                }
                Some(plate) => {
                    for m_p_info in plate.iter() {
                        let mut pl_info = DetailPlateInfo {
                            id: m_p_info.plate_id,
                            code: m_p_info.plate_code.clone(),
                            name: m_p_info.plate_name.clone(),
                            count: 5,
                            stock_infos: vec![],
                        };
                        let mut count = 5;
                        match self.stock_extent_info_belong_plate.read().await.get(&pl_info.id) {
                            None => {
                                info!("信息不完整");
                            }
                            Some(bt_map) => {
                                //                                info!("{:?}",bt_map.value());
                                for value in bt_map.iter().rev() {
                                    // if count == 0{
                                    //     break;
                                    // }
                                    for vec_val in value.1 {
                                        if count == 0 {
                                            break;
                                        }
                                        let mut s_info = DetailCodeInfo {
                                            code: vec_val.clone(),
                                            ..Default::default()
                                        };
                                        if let Some(s_info_copy) = self.stock_tick.read().await.get(vec_val) {
                                            s_info.last_price = s_info_copy.q_last_price;
                                            s_info.change_value = s_info_copy.q_change_value;
                                            s_info.change_rate = s_info_copy.q_change_rate;
                                            //pl_info.stock_infos.push(s_info);
                                            //  count-=1;
                                            //                                            info!("{:?}",count);
                                        }
                                        pl_info.stock_infos.push(s_info);
                                        count -= 1;
                                    }
                                }
                            }
                        }
                        res.hot_plate.push(pl_info);
                    }
                }
            }
        } else if in_stream.market_type == 2 {
            match self.market_plate.read().await.get(&in_stream.market_type) {
                None => {
                    info!("not fount market_plate");
                }
                Some(plate) => {
                    // info!("{:?}", plate);
                    for m_p_info in plate.iter() {
                        let mut pl_info = DetailPlateInfo {
                            id: m_p_info.plate_id,
                            code: m_p_info.plate_code.clone(),
                            name: m_p_info.plate_name.clone(),
                            count: 5,
                            stock_infos: vec![],
                        };
                        if m_p_info.plate_code == "ZG0001_US".to_string() {
                            continue;
                        }
                        let mut count = 5;
                        match self.plate_stock.read().await.get(&pl_info.id) {
                            None => {
                                info!("信息不完整");
                            }
                            Some(stock_list) => {
                                //                                  info!("{:?}",stock_list.value());
                                match stock_list.iter().find(|x| **x == "LGHL_XNAS".to_string()) {
                                    None => {}
                                    Some(val) => {
                                        if let Some(s_info_copy) = self.stock_tick.read().await.get(val) {
                                            let s_info = DetailCodeInfo {
                                                code: val.clone(),
                                                last_price: s_info_copy.q_last_price,
                                                change_value: s_info_copy.q_change_value,
                                                change_rate: s_info_copy.q_change_rate,
                                                top_stock: "".to_string(),
                                            };
                                            //                                          info!("{:?}",s_info);
                                            pl_info.stock_infos.push(s_info);
                                            count -= 1;
                                        }
                                    }
                                }

                                for value in stock_list.iter() {
                                    if count == 0 {
                                        break;
                                    }
                                    if value == "LGHL_XNAS".to_string().as_str() {
                                        continue;
                                    }
                                    let mut s_info = DetailCodeInfo {
                                        code: value.clone(),
                                        last_price: 0.0,
                                        change_value: 0.0,
                                        change_rate: 0.0,
                                        top_stock: "".to_string(),
                                    };
                                    if let Some(s_info_copy) = self.stock_tick.read().await.get(value) {
                                        //                                  info!("{:?}",s_info);
                                        s_info.last_price = s_info_copy.q_last_price;
                                        s_info.change_value = s_info_copy.q_change_value;
                                        s_info.change_rate = s_info_copy.q_change_rate;
                                    }
                                    pl_info.stock_infos.push(s_info);
                                    count -= 1;
                                    //  }
                                }
                            }
                        }
                        res.hot_plate.push(pl_info);
                    }
                }
            }
        }

        Ok(res)
    }
    pub async fn post_plate_msg(&self, in_stream: ReqDetailMsg) -> Result<ResultPlateInfoMsg> {
        let mut res = ResultPlateInfoMsg { ..Default::default() };
        //查询热门板块
        if in_stream.info_type == 2 {
            match self.market_plate_info.read().await.get(&in_stream.market_type) {
                None => {
                    info!("not found market_plate info ");
                }
                Some(val) => {
                    // info!("{:?}", val);
                    let mut count = in_stream.count;
                    let list: Vec<_> = self.stock_extent_info.read().await.iter().rev().map(|x| x.1.to_owned()).collect();
                    for all_info in list.iter() {
                        // info!("{:?}", all_info);
                        if count == 0 {
                            break;
                        }
                        for all_info_val in all_info.iter() {
                            // info!("{:?}", all_info_val);
                            //获取板块属性
                            if count == 0 {
                                break;
                            }
                            match self.plate_type.read().await.get(all_info_val) {
                                None => {
                                    info!("not found in plate_type");
                                    continue;
                                }
                                Some(pt) => {
                                    // info!("{:?}", pt);
                                    if *pt == 3 {
                                        if let Some(_index) = val.iter().position(|x| x == all_info_val.clone().as_str()) {
                                            let mut pl_info = DetailCodeInfo {
                                                code: all_info_val.clone(),
                                                last_price: 0.0,
                                                change_value: 0.0,
                                                change_rate: 0.0,
                                                top_stock: "".to_string(),
                                            };
                                            match self.stock_tick.read().await.get(all_info_val.clone().as_str()) {
                                                None => {
                                                    info!("not found stock hq info");
                                                }
                                                Some(hq_val) => {
                                                    // info!("{:?}", hq_val);
                                                    pl_info.code = all_info_val.clone();
                                                    pl_info.last_price = hq_val.q_last_price;
                                                    pl_info.change_rate = hq_val.q_change_rate;
                                                    pl_info.change_value = hq_val.q_change_value;
                                                }
                                            }
                                            match self.plate_name_info.read().await.get(&in_stream.market_type) {
                                                None => {
                                                    info!("not found plate_name_info");
                                                    continue;
                                                }
                                                Some(p_name_info) => match p_name_info.get(all_info_val.clone().as_str()) {
                                                    None => {
                                                        info!("not found plate_name_info");
                                                        continue;
                                                    }
                                                    Some(p_name_info_val) => match self.stock_extent_info_belong_plate.read().await.get(&p_name_info_val.plate_id) {
                                                        None => {
                                                            info!("not found stock_extent_info_belong_plate");
                                                            continue;
                                                        }
                                                        Some(pl_stock_val) => {
                                                            for val in pl_stock_val.iter().rev() {
                                                                if val.1.len() != 0 {
                                                                    pl_info.top_stock = val.1[0].clone();
                                                                    break;
                                                                } else {
                                                                    continue;
                                                                }
                                                            }
                                                        }
                                                    },
                                                },
                                            }
                                            res.plate_infos.push(pl_info.clone());
                                            count -= 1;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //板块详情
        if in_stream.info_type == 3 && in_stream.plate_code != 0 {
            let mut count = in_stream.count;
            if in_stream.plate_code == 19999 {
                let list: Vec<_> = self.stock_extent_info.read().await.iter().rev().map(|x| x.1.to_owned()).collect();
                for val in list {
                    if count == 0 {
                        break;
                    }
                    for s_code in val.iter() {
                        if count == 0 {
                            break;
                        }
                        let mut pl_info = DetailCodeInfo {
                            code: "".to_string(),
                            last_price: 0.0,
                            change_value: 0.0,
                            change_rate: 0.0,
                            top_stock: "".to_string(),
                        };
                        if self.plate_type.read().await.contains_key(s_code) {
                            continue;
                        }
                        if let Some(pl_info_some) = self.market_index.read().await.get(&in_stream.market_type) {
                            if let Some(_pl) = pl_info_some.iter().find(|x| *x == s_code) {
                                info!("find:{:?}", s_code);
                                continue;
                            }
                        }
                        match self.stock_belong_market.read().await.get(s_code) {
                            None => {
                                info!("not found stock_belong_market");
                                continue;
                            }
                            Some(mk_id) => {
                                if *mk_id != in_stream.market_type {
                                    continue;
                                }
                            }
                        }
                        match self.stock_tick.read().await.get(s_code.clone().as_str()) {
                            None => {
                                info!("not found stock hq info");
                                continue;
                            }
                            Some(hq_val) => {
                                pl_info.code = s_code.clone();
                                pl_info.last_price = hq_val.q_last_price;
                                pl_info.change_rate = hq_val.q_change_rate;
                                pl_info.change_value = hq_val.q_change_value;
                                count -= 1;
                            }
                        }
                        res.plate_infos.push(pl_info);
                    }
                }
            }
            if in_stream.plate_code == 29999 {
                let mut count = in_stream.count;
                let list: Vec<_> = self.stock_extent_info.read().await.iter().map(|x| x.1.to_owned()).collect();
                for val in list.iter() {
                    if count == 0 {
                        break;
                    }
                    for s_code in val.iter() {
                        if count == 0 {
                            break;
                        }
                        let mut pl_info = DetailCodeInfo {
                            code: "".to_string(),
                            last_price: 0.0,
                            change_value: 0.0,
                            change_rate: 0.0,
                            top_stock: "".to_string(),
                        };
                        if self.plate_type.read().await.contains_key(s_code) {
                            continue;
                        }
                        if let Some(pl_info_some) = self.market_index.read().await.get(&in_stream.market_type) {
                            if let Some(_pl) = pl_info_some.iter().find(|x| *x == s_code) {
                                info!("find:{:?}", s_code);
                                continue;
                            }
                        }
                        match self.stock_belong_market.read().await.get(s_code) {
                            None => {
                                info!("not found stock_belong_market");
                                continue;
                            }
                            Some(mk_id) => {
                                if *mk_id != in_stream.market_type {
                                    continue;
                                }
                            }
                        }
                        match self.stock_tick.read().await.get(s_code.clone().as_str()) {
                            None => {
                                info!("not found stock hq info");
                                continue;
                            }
                            Some(hq_val) => {
                                pl_info.code = s_code.clone();
                                pl_info.last_price = hq_val.q_last_price;
                                pl_info.change_rate = hq_val.q_change_rate;
                                pl_info.change_value = hq_val.q_change_value;
                                count -= 1;
                            }
                        }
                        res.plate_infos.push(pl_info);
                    }
                }
            }
            if in_stream.plate_code != 19999 && in_stream.plate_code != 29999 {
                //其他板块
                match self.plate_name_info.read().await.get(&in_stream.market_type) {
                    None => {
                        info!("not found in plate_name_info")
                    }
                    Some(_val) => {
                        match self.stock_extent_info_belong_plate.read().await.get(&in_stream.plate_code) {
                            None => {
                                info!("not found in stock_extent_info_belong_plate")
                            }
                            Some(stock_info) => {
                                //                                        info!("{:?}",stock_info.value());
                                for si in stock_info.iter().rev() {
                                    if count == 0 {
                                        break;
                                    }
                                    let mut pl_info = DetailCodeInfo {
                                        code: "".to_string(),
                                        last_price: 0.0,
                                        change_value: 0.0,
                                        change_rate: 0.0,
                                        top_stock: "".to_string(),
                                    };
                                    for s_c in si.1.iter() {
                                        match self.stock_tick.read().await.get(s_c.clone().as_str()) {
                                            None => {
                                                info!("not found stock hq info");
                                                pl_info.code = s_c.clone();
                                                pl_info.last_price = 0.0;
                                                pl_info.change_rate = 0.0;
                                                pl_info.change_value = 0.0;
                                                res.plate_infos.push(pl_info.clone());
                                                info!("=====================================");
                                                self.stock_tick.write().await.insert(s_c.clone(), YsHqInfo::default());
                                                info!("=====================================");
                                                count -= 1;
                                            }
                                            Some(hq_val) => {
                                                pl_info.code = s_c.clone();
                                                pl_info.last_price = hq_val.q_last_price;
                                                pl_info.change_rate = hq_val.q_change_rate;
                                                pl_info.change_value = hq_val.q_change_value;
                                                res.plate_infos.push(pl_info.clone());
                                                count -= 1;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        Ok(res)
    }

    pub async fn return_hq(&self, hq_info: &protofiles::hqcenter::YsHqInfo) -> messagecenter::protofiles::hqmsg::YsHqInfo {
        let hq = YsHqInfo {
            exchange_id: hq_info.exchange_id.clone(),
            commodity_no: hq_info.commodity_no.clone(),
            contract_no1: hq_info.contract_no1.clone(),
            currency_no: hq_info.currency_no.clone(),
            tapidtstamp: hq_info.tapidtstamp.clone(),
            q_pre_settle_price: hq_info.q_pre_settle_price,
            q_pre_position_qty: hq_info.q_pre_position_qty,
            q_opening_price: hq_info.q_opening_price,
            q_last_price: hq_info.q_last_price,
            q_high_price: hq_info.q_high_price,
            q_low_price: hq_info.q_low_price,
            q_limit_up_price: hq_info.q_limit_up_price,
            q_limit_down_price: hq_info.q_limit_down_price,
            q_total_qty: hq_info.q_total_qty,
            q_total_turnover: hq_info.q_total_turnover,
            q_position_qty: hq_info.q_position_qty,
            q_average_price: hq_info.q_average_price,
            q_closing_price: hq_info.q_closing_price,
            q_last_qty: hq_info.q_last_qty,
            q_bid_price: hq_info.q_bid_price.clone(),
            q_bid_qty: hq_info.q_bid_qty.clone(),
            q_ask_price: hq_info.q_ask_price.clone(),
            q_ask_qty: hq_info.q_ask_qty.clone(),
            q_change_rate: hq_info.q_change_rate,
            q_change_value: hq_info.q_change_value,
            q_pre_closing_price: hq_info.q_pre_closing_price,
            q_total_bid_qty: hq_info.q_total_bid_qty,
            q_total_ask_qty: hq_info.q_total_ask_qty,
            ..Default::default()
        };
        return hq;
    }

    pub async fn post_trs_info_msg(&self, in_stream: ReqTrsMsg) -> Result<ResultPlateInfoMsg> {
        let mut res = ResultPlateInfoMsg { ..Default::default() };
        let count = in_stream.count;
        if in_stream.order_type == 0 {
            let list = self.stock_tick.read().await;
            let mut bt_map: BTreeMap<i32, Vec<String>> = BTreeMap::new();
            for (_k, val) in list.iter() {
                match bt_map.get_mut(&((val.q_last_price * 100.0) as i32)) {
                    None => {
                        let mut new_list = Vec::new();
                        new_list.push(val.contract_no1.clone());
                        bt_map.insert((val.q_last_price * 100.0) as i32, new_list);
                    }
                    Some(btree_list) => {
                        btree_list.push(val.contract_no1.clone());
                    }
                }
            }
            let ret_list = self.get_price(bt_map, count, in_stream.order).await;
            let mut ret = ret_list.clone();
            info!("{:?}", ret);
            res.plate_infos.append(&mut ret);
        } else {
            //涨跌幅
            let list = self.stock_extent_info.clone();
            if in_stream.order {
                let ret_list = self.get_rev_list(list, count).await;
                let mut ret = ret_list.clone();
                info!("{:?}", ret);
                res.plate_infos.append(&mut ret);
            } else {
                let ret_list = self.get_list(list, count).await;
                let mut ret = ret_list.clone();
                info!("{:?}", ret);
                res.plate_infos.append(&mut ret);
            }
        }
        Ok(res)
    }
    pub async fn get_list(&self, list: Arc<RwLock<BTreeMap<i32, Vec<String>>>>, mut count: i32) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let list_s: Vec<_> = list.read().await.iter().map(|x| x.1.to_owned()).collect();
        let market_index = self.market_index.read().await;
        for val in list_s.iter() {
            if count == 0 {
                break;
            }
            for vec_val in val {
                if count == 0 {
                    break;
                }
                let mut pl_info = DetailCodeInfo {
                    code: "".to_string(),
                    last_price: 0.0,
                    change_value: 0.0,
                    change_rate: 0.0,
                    top_stock: "".to_string(),
                };
                if self.trs_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                    continue;
                }
                let flag = false;
                for (_k, val) in market_index.iter() {
                    if !val.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        continue;
                    }
                }
                if flag {
                    continue;
                }
                match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                    None => {
                        info!("not found stock hq info");
                    }
                    Some(hq_val) => {
                        pl_info.code = vec_val.clone();
                        pl_info.last_price = hq_val.q_last_price;
                        pl_info.change_rate = hq_val.q_change_rate;
                        pl_info.change_value = hq_val.q_change_value;
                        pl_list.push(pl_info.clone());
                        count -= 1;
                    }
                }
            }
        }
        return pl_list;
    }
    pub async fn get_rev_list(&self, list: Arc<RwLock<BTreeMap<i32, Vec<String>>>>, mut count: i32) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let market_index = self.market_index.read().await;
        let list_s: Vec<_> = list.read().await.iter().rev().map(|x| x.1.to_owned()).collect();
        for val in list_s {
            if count == 0 {
                break;
            }
            for vec_val in val {
                if count == 0 {
                    break;
                }
                let mut pl_info = DetailCodeInfo {
                    code: "".to_string(),
                    last_price: 0.0,
                    change_value: 0.0,
                    change_rate: 0.0,
                    top_stock: "".to_string(),
                };
                if self.trs_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                    continue;
                }
                let mut flag = false;
                for (_k, val) in market_index.iter() {
                    if !val.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        flag = true;
                        break;
                    }
                }
                if flag {
                    continue;
                }
                match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                    None => {}
                    Some(hq_val) => {
                        pl_info.code = vec_val.clone();
                        pl_info.last_price = hq_val.q_last_price;
                        pl_info.change_rate = hq_val.q_change_rate;
                        pl_info.change_value = hq_val.q_change_value;
                        pl_list.push(pl_info.clone());
                        count -= 1;
                    }
                }
            }
        }
        return pl_list;
    }
    pub async fn get_price(&self, list: BTreeMap<i32, Vec<String>>, mut count: i32, x: bool) -> Vec<DetailCodeInfo> {
        let mut pl_list = Vec::new();
        let market_index = self.market_index.read().await;
        if x {
            for val in list.iter().rev() {
                if count == 0 {
                    break;
                }
                for vec_val in val.1 {
                    if count == 0 {
                        break;
                    }
                    let mut pl_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if self.trs_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        continue;
                    }
                    let mut flag = false;
                    for (_k, val) in market_index.iter() {
                        if !val.iter().find(|x| *x == &vec_val.clone()).is_none() {
                            flag = true;
                            break;
                        }
                    }
                    if flag {
                        continue;
                    }
                    match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                        None => {
                            info!("not found stock hq info");
                        }
                        Some(hq_val) => {
                            pl_info.code = vec_val.clone();
                            pl_info.last_price = hq_val.q_last_price;
                            pl_info.change_rate = hq_val.q_change_rate;
                            pl_info.change_value = hq_val.q_change_value;
                            pl_list.push(pl_info.clone());
                            count -= 1;
                        }
                    }
                }
            }
        } else {
            for val in list.iter() {
                if count == 0 {
                    break;
                }
                for vec_val in val.1 {
                    if count == 0 {
                        break;
                    }
                    let mut pl_info = DetailCodeInfo {
                        code: "".to_string(),
                        last_price: 0.0,
                        change_value: 0.0,
                        change_rate: 0.0,
                        top_stock: "".to_string(),
                    };
                    if self.trs_stock_info.read().await.iter().find(|x| *x == &vec_val.clone()).is_none() {
                        continue;
                    }
                    let mut flag = false;
                    for (_k, val) in market_index.iter() {
                        if !val.iter().find(|x| *x == &vec_val.clone()).is_none() {
                            flag = true;
                            break;
                        }
                    }
                    if flag {
                        continue;
                    }
                    match self.stock_tick.read().await.get(vec_val.clone().as_str()) {
                        None => {
                            info!("not found stock hq info");
                        }
                        Some(hq_val) => {
                            pl_info.code = vec_val.clone();
                            pl_info.last_price = hq_val.q_last_price;
                            pl_info.change_rate = hq_val.q_change_rate;
                            pl_info.change_value = hq_val.q_change_value;
                            pl_list.push(pl_info.clone());
                            count -= 1;
                        }
                    }
                }
            }
        }

        return pl_list;
    }
}
