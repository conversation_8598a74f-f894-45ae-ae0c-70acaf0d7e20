// use utility::timeutil::{self, current_naive_time};

// use anyhow::Result;
// use std::vec;
// use std::{collections::HashMap, sync::Arc};

// use crate::client::marketdataclient::MarketDataclient;
// use crate::protofiles::YsHqInfo;
use tracing::error;
#[derive(Clone)]
pub struct StreamCenterController {
    // pub mktclient: Arc<MarketDataclient>,
}

impl StreamCenterController {
    // async fn get_hq(&self, in_stream: Streaming<ContractMsg>) -> Result<YsHqInfo> {}
}
use common::logclient::LogClient;

pub async fn push_log(msg: &str) {
    error!("{:?}", msg);
    if let Ok(c) = LogClient::get() {
        c.push_error(msg).await;
    }
}
