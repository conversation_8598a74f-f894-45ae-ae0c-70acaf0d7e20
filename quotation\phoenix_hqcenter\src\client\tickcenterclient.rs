use anyhow::Result;
use tonic::transport::Channel;
use tonic::Request;
use tracing::*;

use protoes::phoenixtickcenter::{phoenix_tick_center_client::PhoenixTickCenterClient, LastPriceReq, LastPriceResp, TickReq, TickResp};

#[derive(Clone)]
pub struct TickCenterClient {
    uri_hs: String,
    uri_hk: String,
    pub client_hs: Option<PhoenixTickCenterClient<Channel>>,
    pub client_hk: Option<PhoenixTickCenterClient<Channel>>,
}

impl TickCenterClient {
    pub async fn new(uri_hs: &String, uri_hk: &String) -> Self {
        info!("HS_TICK server: {}, HK_TICK server: {}", uri_hs, uri_hk);
        let mut tick_client = TickCenterClient {
            uri_hs: uri_hs.to_owned(),
            uri_hk: uri_hk.to_owned(),
            client_hs: None,
            client_hk: None,
        };
        let ret = PhoenixTickCenterClient::connect(uri_hs.to_owned()).await;
        if ret.as_ref().is_err() {
            // push_log(format!("connect to HS_TICK failed: {:?}", uri).as_str()).await;
            error!("HS_TICK连接失败: {:?}", ret);
        } else {
            info!("HS_TICK连接成功....");
            tick_client.client_hs = Some(ret.unwrap());
        }

        let ret = PhoenixTickCenterClient::connect(uri_hk.to_owned()).await;
        if ret.as_ref().is_err() {
            // push_log(format!("connect to HK_TICK failed: {:?}", uri).as_str()).await;
            error!("HK_TICK连接失败: {:?}", ret);
        } else {
            info!("HK_TICK连接成功....");
            tick_client.client_hk = Some(ret.unwrap());
        }
        tick_client
    }

    pub async fn init_client_hs(&mut self) -> Result<PhoenixTickCenterClient<Channel>> {
        if self.client_hs.is_some() {
            return Ok(self.client_hs.clone().unwrap());
        } else {
            let ret = PhoenixTickCenterClient::connect(self.uri_hs.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to hs tickcenter failed: {:?}", self.uri_hs).as_str()).await;
                return Err(anyhow!(format!("connect to HS_TICK failed")));
            }
            info!("HS_TICK连接成功....");
            self.client_hs = Some(ret.expect("connect to HS_TICK failed"));
            return Ok(self.client_hs.clone().unwrap());
        }
    }

    pub async fn init_client_hk(&mut self) -> Result<PhoenixTickCenterClient<Channel>> {
        if self.client_hk.is_some() {
            return Ok(self.client_hk.clone().unwrap());
        } else {
            let ret = PhoenixTickCenterClient::connect(self.uri_hk.to_owned()).await;
            if ret.as_ref().is_err() {
                // push_log(format!("connect to HK_TICK failed: {:?}", self.uri_hs).as_str()).await;
                return Err(anyhow!(format!("connect to HK_TICK failed")));
            }
            info!("HK_TICK连接成功....");
            self.client_hk = Some(ret.expect("connect to HK_TICK failed"));
            return Ok(self.client_hk.clone().unwrap());
        }
    }

    pub async fn get_last_price(&mut self, contract_no: &String) -> Result<LastPriceResp> {
        let ret = self.get_connect(contract_no).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (flag, mut client) = ret.unwrap();

        let req_data = LastPriceReq { contract_nos: contract_no.to_owned() };
        info!("last price req: {:?}", req_data);
        match client.get_last_price(Request::new(req_data)).await {
            Ok(value) => {
                return Ok(value.into_inner());
            }
            Err(err) => {
                if flag == 1 {
                    self.client_hs = None;
                } else {
                    self.client_hk = None;
                }
                error!("获取最新价失败: {:?}", err);
                return Err(anyhow!("获取最新价失败: {:?}", err));
            }
        }
    }

    pub async fn get_latest_tick(&mut self, contract_no: &String) -> Result<TickResp> {
        let ret = self.get_connect(contract_no).await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let (flag, mut client) = ret.unwrap();

        let req = TickReq { contract_no: contract_no.to_owned() };
        match client.get_tick_hq(Request::new(req)).await {
            Ok(value) => {
                return Ok(value.into_inner());
            }
            Err(err) => {
                if flag == 1 {
                    self.client_hs = None;
                } else {
                    self.client_hk = None;
                }
                error!("获取最新tick失败: {:?}", err);
                return Err(anyhow!("获取最新tick失败: {:?}", err));
            }
        }
    }

    async fn get_connect(&mut self, contract_no: &String) -> Result<(i64, PhoenixTickCenterClient<Channel>)> {
        let mut flag = 1;
        let ret = self.init_client_hs().await;
        if ret.as_ref().is_err() {
            error!("{:?}", ret.as_ref().err().unwrap());
            return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
        }
        let mut client = ret.unwrap();
        if contract_no.contains("XHKG") || contract_no.contains("HK") {
            let ret = self.init_client_hk().await;
            if ret.as_ref().is_err() {
                error!("{:?}", ret.as_ref().err().unwrap());
                return Err(anyhow!(ret.as_ref().err().unwrap().to_string()));
            }
            client = ret.unwrap();
            flag = 2;
        }
        return Ok((flag, client));
    }
}
