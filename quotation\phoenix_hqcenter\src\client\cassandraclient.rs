use anyhow::Result;
use scylla::{transport::session::PoolSize, QueryRowsResult, Session, SessionBuilder};
// use scylla::client::session::Session;
// use scylla::client::session_builder::SessionBuilder;
// use scylla::client::PoolSize;
// use scylla::response::query_result::QueryRowsResult;
// use scylla::statement::batch::Batch;
// use scylla::statement::prepared::PreparedStatement;
use std::num::NonZeroUsize;
use std::sync::Arc;
use tracing::*;
// use serde::Deserialize;
// use scylla::query::Query;
// use std::collections::HashMap;
// use std::fmt::Write;

use crate::config::settings::CassandraConfig;

pub struct CassandraClient {
    pub hs_namespace: String,
    pub hk_namespace: String,
    pub session: Option<Arc<Session>>, //该连接用于执行Sql语句
}

impl CassandraClient {
    pub async fn new(config: &CassandraConfig) -> Result<Self> {
        let mut client = CassandraClient {
            hs_namespace: config.hs_namespace.clone(),
            hk_namespace: config.hk_namespace.clone(),
            session: None,
        };
        match SessionBuilder::new()
            .known_node(&config.addr)
            .user(&config.username, &config.password)
            .pool_size(PoolSize::PerHost(NonZeroUsize::new(16).unwrap()))
            // .schema_agreement_interval(std::time::Duration::from_secs(5))
            .keyspaces_to_fetch([config.hs_namespace.clone(), config.hk_namespace.clone()])
            .keepalive_interval(std::time::Duration::from_secs(2))
            .build()
            .await
        {
            Ok(session) => {
                // session.use_keyspace(&config.hs_namespace.clone(), true).await.expect("user namespace error......");
                info!("Cassandra 连接成功...");
                client.session = Some(Arc::new(session));
            }
            Err(err) => {
                // .expect("连接服务器失败");
                error!("connect Cassandra err: {:?}", err);
            }
        };

        Ok(client)
    }

    #[allow(dead_code)]
    pub async fn execute_cql(&self, cql: &String) -> Result<()> {
        // 检查 session 是否存在
        let session = match &self.session {
            Some(s) => s,
            None => {
                error!("未使用Cassandra");
                return Ok(());
            }
        };
        // 准备 CQL 查询
        let prepared = session.prepare(cql.as_str()).await?;

        // 执行查询
        session.execute_unpaged(&prepared, &[]).await?;
        Ok(())
    }

    pub async fn query_cass(&self, cql: &String, contractno: &String) -> Option<QueryRowsResult> {
        // 检查 session 是否存在
        let session = match &self.session {
            Some(s) => s,
            None => {
                error!("未使用Cassandra");
                return None;
            }
        };
        // 记录开始时间
        let now = std::time::Instant::now();

        // 根据 contractno 选择 keyspace
        if contractno.contains("XSHE") || contractno.contains("XSHG") || contractno.contains("HS") {
            if let Err(e) = session.use_keyspace(&self.hs_namespace, false).await {
                error!("切换 keyspace 失败: {:?}", e);
                return None;
            }
        } else if contractno.contains("XHKG") || contractno.contains("HK") {
            if let Err(e) = session.use_keyspace(&self.hk_namespace, false).await {
                error!("切换 keyspace 失败: {:?}", e);
                return None;
            }
        }

        // 准备 CQL 查询
        let prepared = match session.prepare(cql.as_str()).await {
            Ok(p) => p,
            Err(e) => {
                error!("准备 CQL 查询失败: {:?}", e);
                return None;
            }
        };

        // 执行查询
        match session.execute_unpaged(&prepared, ()).await {
            Ok(result) => {
                info!("查询用时: {:?}", now.elapsed());
                match result.into_rows_result() {
                    Ok(rows) => Some(rows),
                    Err(e) => {
                        error!("转换查询结果失败: {:?}", e);
                        None
                    }
                }
            }
            Err(e) => {
                info!("查询用时: {:?}", now.elapsed());
                error!("执行 CQL 查询失败: {:?}", e);
                None
            }
        }
    }
}
