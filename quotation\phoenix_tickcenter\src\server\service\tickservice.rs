use anyhow::Result;
use chrono::{Datelike, Timelike};
use dashmap::DashMap;
use lazy_static::lazy_static;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;

use akaclient::akaclient::AkaClient;
use protoes::hqmsg::YsHqInfo;
use utility::timeutil::{current_naive_time, today_weekend};

use crate::commonutil::commonutil::CommonUtil;

lazy_static! {
    static ref SH_DATE_FLAG: RwLock<bool> = RwLock::new(true);
    static ref SZ_DATE_FLAG: RwLock<bool> = RwLock::new(true);
    static ref HK_DATE_FLAG: RwLock<bool> = RwLock::new(true);
}
#[derive(Clone, Debug)]
pub struct TickService {
    pub akaclient: Arc<AkaClient>,
    pub ticks_cache: Arc<DashMap<String, YsHqInfo>>,
    pub cache: Arc<RwLock<Vec<YsHqInfo>>>,
}

impl TickService {
    pub fn new(akaclient: Arc<AkaClient>) -> Self {
        Self {
            akaclient,
            ticks_cache: Arc::new(DashMap::new()),
            cache: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn init_tick_cache(&self, ticks: &Vec<YsHqInfo>) {
        for hqinfo in ticks.iter() {
            self.ticks_cache.insert(hqinfo.contract_no1.to_owned(), hqinfo.to_owned());
        }

        // ticks.iter().map(|&x| self.ticks_cache.insert(x.contract_no1.clone(), x));
    }

    pub async fn get_market_id(exchange_code: &str) -> i64 {
        match exchange_code {
            "XSHG" | "HS" => 101,
            "XSHE" => 102,
            "XHKG" | "HK" => 103,
            "XASE" => 104,
            "XNYS" => 105,
            "XNAS" => 106,
            _ => 0,
        }
    }

    ///检查市场交易日
    pub async fn check_trade_day(&self, exchange_id: &str) -> bool {
        let market_id = Self::get_market_id(exchange_id).await;
        let ret = self.akaclient.query_market_info(market_id).await;
        if ret.is_err() {
            error!("获取交易日信息失败: {}", ret.err().unwrap());
            return false;
        }
        let market_info = ret.unwrap();
        if market_id == 101 {
            let mut flag = SH_DATE_FLAG.write().await;
            if *flag {
                info!("获取沪深交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else if market_id == 102 {
            let mut flag = SZ_DATE_FLAG.write().await;
            if *flag {
                info!("获取沪深交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else if market_id == 103 {
            let mut flag = HK_DATE_FLAG.write().await;
            if *flag {
                info!("获取港股交易日信息");
                info!("ret: {:?}", market_info);
                *flag = false;
            }
        } else {
            info!("ret: {:?}", market_info);
        }
        // info!("market_info: {:?}", market_info);
        if market_info.date_type == 0 || today_weekend() {
            error!("当前时间不是交易日：{}", market_info.current_date);
            return false;
        }

        true
    }

    pub async fn insert_ticks(&self, hqinfo: &mut YsHqInfo, common_util: &CommonUtil) -> Result<()> {
        if !self.check_trade_day(&hqinfo.exchange_id).await {
            error!("当前日期不是交易日,不处理: code: {}, time: {}", hqinfo.contract_no1, hqinfo.tapidtstamp);
            return Err(anyhow!("当前日期不是交易日,不处理: code: {}, time: {}", hqinfo.contract_no1, hqinfo.tapidtstamp));
        }

        let (ret_time, flag) = common_util.check_hqtick_time(&hqinfo.tapidtstamp[11..19], &hqinfo.exchange_id).await;

        //不是特殊时间，也不是交易时间内，不处理
        if flag < 0 && common_util.trade_area_index(&hqinfo.tapidtstamp[11..19], &hqinfo.exchange_id).await.is_err() {
            return Err(anyhow!("该条tick不在交易时间内,不处理: code: {}, time: {}", hqinfo.contract_no1, hqinfo.tapidtstamp));
        }
        if flag == 2 || flag == 3 {
            hqinfo.tapidtstamp = hqinfo.tapidtstamp[0..11].to_owned() + &ret_time;
        } else if flag == 1 {
            if hqinfo.q_last_price == 0.0 {
                return Err(anyhow!("开盘集合竞价数据不发送MQ..."));
            }
            // self.ticks_cache.insert(hqinfo.contract_no1.to_owned(), hqinfo.to_owned());
        }
        self.ticks_cache.insert(hqinfo.contract_no1.to_owned(), hqinfo.to_owned());
        {
            self.cache.write().await.push(hqinfo.to_owned());
        }

        Ok(())
    }

    #[allow(dead_code)]
    pub async fn query_ticks(&self, key: &Vec<String>) -> Vec<YsHqInfo> {
        let mut ret: Vec<YsHqInfo> = Vec::new();
        for val in key.iter() {
            let hqinfo = self.ticks_cache.get(val);
            if hqinfo.is_none() {
                continue;
            }
            ret.push(hqinfo.unwrap().value().clone());
        }
        ret
    }

    pub async fn query_tick(&self, key: &str) -> Option<YsHqInfo> {
        // let hqinfo = self.ticks_cache.get(key);
        // if hqinfo.is_none() {
        //     return None;
        // }
        // Some(hqinfo.unwrap().value().to_owned())

        self.ticks_cache.get(key).map(|tick| tick.value().to_owned())
    }

    #[allow(dead_code)]
    pub async fn insert_tick_cache(&self, hqinfo: &YsHqInfo) {
        self.ticks_cache.insert(hqinfo.contract_no1.to_owned(), hqinfo.to_owned());
    }

    pub async fn get_ticks(&self, stock_code: &str) -> Vec<YsHqInfo> {
        let mut ticks = Vec::new();
        // let ticks_cache_clone = self.ticks_cache.clone();

        if stock_code.is_empty() {
            //获取所有
            for tick in self.ticks_cache.iter() {
                ticks.push(tick.value().to_owned());
            }
        } else {
            for code in stock_code.split(',').map(str::trim).filter(|c| !c.is_empty()) {
                if let Some(tick) = self.ticks_cache.get(code) {
                    let tickinfo = tick.value().to_owned();
                    info!("{:?}", tickinfo);
                    ticks.push(tickinfo);
                } else {
                    error!("找不到{}的tick数据", code);
                }
            }
            // if stock_code.contains(",") {
            //     let codes: Vec<&str> = stock_code.split(",").collect();
            //     for code in codes {
            //         let tick = self.ticks_cache.get(code);
            //         if tick.is_none() {
            //             error!("找不到{}的tick数据", code);
            //             continue;
            //         }
            //         let tickinfo = tick.unwrap().value().to_owned();
            //         info!("{:?}", tickinfo);
            //         ticks.push(tickinfo);
            //     }
            // } else {
            //     let tick = self.ticks_cache.get(stock_code);
            //     if tick.is_none() {
            //         error!("找不到{}的tick数据", &stock_code);
            //     } else {
            //         let tickinfo = tick.unwrap().value().to_owned();
            //         info!("{:?}", tickinfo);
            //         ticks.push(tickinfo);
            //     }
            // }
        }

        ticks
    }

    #[allow(dead_code)]
    pub async fn deadline(exchange: &str) -> i64 {
        let mut st = current_naive_time();

        if exchange == "US" || exchange == "XASE" || exchange == "XNYS" || exchange == "XNAS" {
            //美股
            if st.hour() >= 21 {
                //明天的4:10
                st = st.with_day(st.day() + 1).unwrap().with_hour(5).unwrap().with_minute(10).unwrap().with_second(0).unwrap()
            } else {
                st = st.with_hour(5).unwrap().with_minute(10).unwrap().with_second(0).unwrap()
            }
        } else if exchange == "HK" || exchange == "XHKG" {
            //港股
            st = st.with_hour(16).unwrap().with_minute(25).unwrap().with_second(0).unwrap()
        } else if exchange == "HS" || exchange == "XSHE" || exchange == "XSHG" {
            //沪深
            st = st.with_hour(15).unwrap().with_minute(2).unwrap().with_second(0).unwrap()
        }
        st.and_utc().timestamp()
    }
}
