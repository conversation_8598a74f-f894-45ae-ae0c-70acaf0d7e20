use anyhow::{anyhow, Result};
use common::logclient::log_error;
use scylla::{batch::Batch, prepared_statement::PreparedStatement, transport::session::PoolSize, Session, SessionBuilder};
// use scylla::client::session::Session;
// use scylla::client::session_builder::SessionBuilder;
// use scylla::client::PoolSize;
// use scylla::statement::batch::Batch;
// use scylla::statement::prepared::PreparedStatement;
use std::{num::NonZeroUsize, sync::Arc};
use tracing::*;
// use serde::Deserialize;
// use std::collections::HashMap;
// use std::fmt::Write;

use crate::config::settings::CassandraConfig;
// use crate::protofiles::YsHqInfo;
use crate::server::service::persistservice::CassandraData;

use futures::stream::{FuturesUnordered, StreamExt};
#[allow(dead_code)]
pub struct CassandraClient {
    pub namespace: String,
    pub session: Arc<Session>, //该连接用于执行Sql语句
}

impl CassandraClient {
    pub async fn new(config: &CassandraConfig) -> Result<Self> {
        match SessionBuilder::new()
            .known_node(&config.addr)
            .user(&config.username, &config.password)
            .use_keyspace(&config.namespace, false)
            .pool_size(PoolSize::PerHost(NonZeroUsize::new(16).unwrap()))
            .build()
            .await
        {
            Ok(session) => {
                info!("Cassandra 连接成功...");
                // session.use_keyspace(&config.namespace, false).await.expect("user namespace error......");

                let client = CassandraClient {
                    namespace: config.namespace.clone(),
                    session: Arc::new(session), //该连接用于执行Sql语句
                };
                return Ok(client);
            }
            Err(e) => {
                error!("Cassandra 连接失败: {}", e);
                log_error(&format!("Cassandra 连接失败: {}, {}", config.addr, e)).await;
                return Err(anyhow!("Cassandra 连接失败: {}", e));
            }
        }

        // let session = SessionBuilder::new()
        //     .known_node(&config.addr)
        //     .user(&config.username, &config.password)
        //     .pool_size(PoolSize::PerHost(NonZeroUsize::new(16).unwrap()))
        //     .build()
        //     .await
        //     .expect("连接服务器失败");

        // session.use_keyspace(&config.namespace, false).await.expect("user namespace error......");

        // info!("Cassandra 连接成功...");

        // let client = CassandraClient {
        //     namespace: config.namespace.clone(),
        //     session,
        // };

        // Ok(client)
    }

    #[allow(unused)]
    pub async fn do_prepare(&self, cql: &str) -> Result<PreparedStatement> {
        let ret = self.session.prepare(cql).await;
        if ret.as_ref().is_err() {
            return Err(anyhow!("prepare cql error"));
        }
        Ok(ret.unwrap())
    }

    #[allow(unused)]
    pub async fn execute_prepare(&self, prepared: &PreparedStatement, val: &CassandraData) -> Result<()> {
        self.session.execute_unpaged(&prepared, &val).await.map_err(|err| {
            error!("执行预编译语句失败: {}", err);
            anyhow!(err)
        })?;

        Ok(())
    }

    #[allow(unused)]
    pub async fn execute_cql(&self, cql: &str, val: &CassandraData) -> Result<()> {
        // // self.session.prepare(&cql).await;
        // let ret = self.session.prepare(cql).await;
        // if ret.as_ref().is_err() {
        //     return Err(anyhow!("prepare cql error"));
        // }
        // let prepared = ret.unwrap();
        // let ret = self.session.execute_unpaged(&prepared, &val).await;
        // // let ret = self.session.query(cql, &[]).await;
        // if ret.as_ref().is_err() {
        //     return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
        // }

        let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
        self.session.execute_unpaged(&prepared, &val).await.map_err(|err| anyhow!("{}", err))?;
        Ok(())
    }

    // pub async fn execute_batch(&self, cql: &str, parms: &Vec<CassandraData>) -> Result<()> {
    //     let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
    //     for val in parms.chunks(8) {
    //         let mut batch: Batch = Default::default();
    //         // 直接为每个 val 添加语句
    //         for _ in val.iter() {
    //             // batch.append_statement(cql);
    //             batch.append_statement(prepared.clone());
    //         }
    //         // batch.set_tracing(true);

    //         // 准备批处理，添加错误处理
    //         let prepared_batch = self.session.prepare_batch(&batch).await.map_err(|err| {
    //             error!("批处理准备失败: {:?}", err);
    //             anyhow!(err)
    //         })?;

    //         // 执行批处理，添加错误处理
    //         self.session.batch(&prepared_batch, &val).await.map_err(|err| {
    //             error!("批处理执行失败: {:?}", err);
    //             anyhow!(err)
    //         })?;
    //     }

    //     // let dst: Vec<Vec<CassandraData>> = parms.chunks(8).map(|s| s.into()).collect();

    //     // for val in dst {
    //     //     let mut batch: Batch = Default::default();
    //     //     // let len = val.len();
    //     //     for _n in val.iter() {
    //     //         batch.append_statement(cql);

    //     //         // let ret = self.session.prepare(cql).await;
    //     //         // if ret.as_ref().is_err() {
    //     //         //     return Err(anyhow!("prepare cql error"));
    //     //         // }
    //     //         // let prepared = ret.unwrap();
    //     //         // batch.append_statement(prepared);
    //     //     }
    //     //     let prepared_batch = self.session.prepare_batch(&batch).await?;
    //     //     if let Err(err) = self.session.batch(&prepared_batch, &val).await {
    //     //         error!("{:?}", err);
    //     //         return Err(anyhow!(err));
    //     //     }
    //     // }
    //     Ok(())
    // }

    pub async fn execute_batch(&self, cql: &str, parms: &Vec<CassandraData>) -> Result<()> {
        let prepared = self.session.prepare(cql).await.map_err(|_| anyhow!("prepare cql error"))?;
        let batch_size = 8; // 可调优
        let concurrency = 4; // 并发批次数

        let mut tasks = FuturesUnordered::new();
        for val in parms.chunks(batch_size) {
            let prepared = prepared.clone();
            let session = self.session.clone();
            let val = val.to_vec();
            tasks.push(tokio::spawn(async move {
                let mut batch: Batch = Default::default();
                for _ in val.iter() {
                    batch.append_statement(prepared.clone());
                }
                let prepared_batch = session.prepare_batch(&batch).await?;
                session.batch(&prepared_batch, &val).await?;
                Ok::<(), anyhow::Error>(())
            }));

            // 控制并发数
            if tasks.len() >= concurrency {
                if let Some(res) = tasks.next().await {
                    res??;
                }
            }
        }
        // 等待剩余任务
        while let Some(res) = tasks.next().await {
            res??;
        }
        Ok(())
    }
}
