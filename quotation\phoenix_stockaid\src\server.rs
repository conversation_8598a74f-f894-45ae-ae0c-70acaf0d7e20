use std::collections::{BTreeMap, HashMap};
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{mpsc, oneshot, RwLock};
use tonic::{self, Request, Response, Status};
use tracing::{error, info};

use messagecenter::quotationclient::QuotationClient;
use protoes::hqmsg::YsHqInfo;
use protoes::phoenixstockaid::{phoenix_stock_aid_server::PhoenixStockAid, ReqDetailMsg, ReqIndexOrRankMsg, ReqTrsMsg, ResultMainIndexMsg, ResultMainRankMsg, ResultPlateInfoMsg};

pub use super::controller::*;
use crate::client::hqclient::HqCenterClient;
use crate::config::settings::Settings;
use crate::dataservice::dbsetup::DbConnection;
use crate::service::wsservice;

type StubType = Arc<ServerController>;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct ServerHandler {
    stub: StubType,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl ServerHandler {
    pub async fn new(settings: &Settings) -> Self {
        // 定时器
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(settings.system.cachelong));
        let hq_center_client = HqCenterClient::new(&settings.servers.hqcenterserver).await;

        let finances_dbconn = DbConnection::new(&settings.database.finances_uri).await;
        let fdb_copy = finances_dbconn.clone();

        let mut stub = ServerController {
            settings: Arc::new(RwLock::new(settings.clone())),
            plate_info: Arc::new(RwLock::new(HashMap::new())),
            index_info: Arc::new(RwLock::new(HashMap::new())),
            stock_info: Arc::new(RwLock::new(HashMap::new())),
            stock_plate: Arc::new(RwLock::new(HashMap::new())),
            plate_stock: Arc::new(RwLock::new(HashMap::new())),
            stock_tick: Arc::new(RwLock::new(HashMap::new())),
            all_stock_info: Arc::new(RwLock::new(Vec::new())),
            stock_extent_info: Arc::new(RwLock::new(BTreeMap::<i32, Vec<String>>::new())),
            stock_extent_info_belong_plate: Arc::new(RwLock::new(HashMap::new())),
            finances_db: Arc::new(fdb_copy.to_owned()),
            hq_center_client,
        };
        info!("init controller");
        let _ = stub.init_controller().await;
        info!("init controller end");

        let (tx_quotation, mut rx_quotation) = tokio::sync::broadcast::channel::<YsHqInfo>(102400);
        let (tx_distribution, mut rx_distribution) = tokio::sync::mpsc::channel::<YsHqInfo>(102400);
        let mut routing_key: HashMap<String, i32> = HashMap::new();
        routing_key.insert("stock.#.#".to_string().clone(), 1);
        let queue_name = format!("phoenix_stockaid_quotation_{}", utility::timeutil::current_timestamp());
        let quotation_client = QuotationClient::new(
            &settings.quotation.stocklive_exchanger.as_str(),
            &queue_name,
            routing_key,
            &format!("{}{}", settings.mq.amqpaddr.as_str(), &settings.quotation.vhost),
            tx_quotation,
        )
        .await;
        // 监听行情
        tokio::spawn(async move {
            messagecenter::init::init_quotation_listen(quotation_client).await;
        });
        let stub = Arc::new(stub);

        let (tx, _rx) = mpsc::channel(16);
        let (tx_close, _rx_close) = oneshot::channel();

        let stub_all = stub.clone();
        let stub_us = stub.clone();
        let sub_c = stub_all.clone();
        let svr_handler = ServerHandler {
            task_dispacther: tx,
            set_close: Some(tx_close),
            stub,
        };

        tokio::spawn(async move {
            let sender_tx_distribution = tx_distribution.clone();
            //   let mut i = 0;
            loop {
                tokio::select! {
                    task = rx_quotation.recv() => {
                        if let Ok(quotation) = task {
                            let _ = sender_tx_distribution.send(quotation).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            let all = stub_all.clone();
            loop {
                tokio::select! {
                    task = rx_distribution.recv() => {
                        if let Some(quotation) = task {
                            let mut stock_tick = all.stock_tick.write().await;
                            // if quotation.contract_no1 == "600000_XSHG" {
                            //     info!("{:?}", quotation);
                            // }
                            let _ =  all.process_hq_info(&mut stock_tick, &quotation.clone()).await;
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            persist_interval.tick().await;
            loop {
                tokio::select! {
                //一个小时序列化一次到文本里
                _ = persist_interval.tick() => {
                    info!("开始序列化");
                    // let cache = stub_cache_json.plate_cache.read().await.clone();
                    // let json_str = serde_json::to_string(&cache).expect("Failed to serialize Map to JSON");
                    // let mut file = File::create("./cache.json").expect("Failed to create file");
                    // file.write_all(json_str.as_bytes()).expect("Failed to write to file");
                }
                }
            }
        });
        let (tx_ws, mut rx_ws) = tokio::sync::mpsc::channel::<YsHqInfo>(102400);
        let mut flag = false;
        if settings.system.wsflag == 1 {
            flag = true;
        }
        if flag {
            let list = stub_us.update_us().await;
            let ss = settings.clone();
            if let Ok(val) = list {
                if val.len() != 0 {
                    tokio::spawn(async move {
                        let val_c = val.clone();
                        while let Err(e) = wsservice::wss_init(&ss, tx_ws.clone(), val_c.clone()).await {
                            error!("{:?}", e);
                        }
                    });
                }
            }
        }

        tokio::spawn(async move {
            let all = sub_c.clone();
            loop {
                tokio::select! {
                    task = rx_ws.recv() => {
                        if let Some(quotation) = task {
                            let mut stock_tick = all.stock_tick.write().await;
                            let _ =  all.process_hq_info(&mut stock_tick, &quotation.clone()).await;
                        }
                    }
                }
            }
        });
        svr_handler
    }
    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

//这里实现grpc的接口
#[tonic::async_trait]
impl PhoenixStockAid for ServerHandler {
    async fn post_main_index_msg(&self, request: Request<ReqIndexOrRankMsg>) -> Result<Response<ResultMainIndexMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取首页指数 request:{:?}", in_stream);
        let res = self.stub.post_main_index_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_main_rank_msg(&self, request: Request<ReqIndexOrRankMsg>) -> Result<Response<ResultMainRankMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取首页热门排行榜 request:{:?}", in_stream);
        let res = self.stub.post_main_rank_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_plate_info_msg(&self, request: Request<ReqDetailMsg>) -> Result<Response<ResultPlateInfoMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("获取板块数据 request:{:?}", in_stream);
        let res = self.stub.post_plate_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }

    async fn post_trs_info_msg(&self, request: Request<ReqTrsMsg>) -> Result<Response<ResultPlateInfoMsg>, Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let in_stream = request.into_inner();
        info!("post_trs_info_msg request:{:?}", in_stream);
        let res = self.stub.post_trs_info_msg(in_stream).await;
        info!("result:{:?}", res);
        return Ok(Response::new(res.unwrap()));
    }
}
