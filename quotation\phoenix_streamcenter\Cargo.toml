[package]
name = "phoenix_streamcenter"
version = "0.2.0"
edition = "2021"
description = "行情流转中心 build time: 2025-04-22"
# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dependencies]
utility = { workspace = true }
common = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
# async-stream = { workspace = true }
tonic = { workspace = true }
futures-util = { workspace = true }
futures = { workspace = true }
futures-lite = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
config = { workspace = true }
# log = { workspace = true }
anyhow = { workspace = true }
# 时间
chrono = { workspace = true }
time = { workspace = true }
# 序列化
serde = { workspace = true }
serde_json = { workspace = true }
# serde_yaml = { workspace = true }
h2 = { workspace = true }
tracing = { workspace = true }
[build-dependencies]
# tonic-build = { workspace = true, features = ["prost"] }
tonic-build.workspace = true

# [[bin]]
# name = "server"
# path = "src/main.rs"
