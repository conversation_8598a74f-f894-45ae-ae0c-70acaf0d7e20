mod algorithm;
mod client;
mod config;
mod controller;
// mod protofiles;
mod server;

use crate::config::settings::Settings;
use anyhow::Result;
use tracing::*;

use common::logclient::*;
use server::ServerHandler;

use protoes::phoenixalgorithmcenter::algorithm_center_server::AlgorithmCenterServer;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // let cfg = "config/algorithmcenter.yaml";
    // loggings::log_init(cfg);
    let prefix = "phoenix_algorithmcenter";
    let dir = "./log";

    let settings = Settings::new().expect("init configurtion error");
    // let level = "INFO";
    let level = &settings.system.loglevel.to_ascii_uppercase();
    let _guard = common::init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    // 1. 日志中心客户端初始化
    init_logclient(&settings.servers.logcenterserver, &format!("{}_{prefix}", &settings.notification.vhost)).await;

    let server = prepare(&settings).await.expect("Init server error......");
    info!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    );
    log_debug(&format!(
        "Server started on {}:{} name: {} version: {} description: {}",
        settings.application.apphost,
        settings.application.appport,
        env!("CARGO_PKG_NAME"),
        env!("CARGO_PKG_VERSION"),
        env!("CARGO_PKG_DESCRIPTION")
    ))
    .await;

    server_run(server, &settings).await
}

async fn prepare(settings: &Settings) -> anyhow::Result<ServerHandler> {
    // let grpc_stub = create_controller(settings).await;

    let grpc = ServerHandler::new(&settings).await;

    Ok(grpc)
}

async fn server_run(mut svr: ServerHandler, settings: &Settings) -> Result<(), Box<dyn std::error::Error>> {
    // let addr = "0.0.0.0:60000".parse().unwrap();
    let addr = format!("{}:{}", settings.application.apphost, settings.application.appport).parse().unwrap();

    // 2. 获取客户端, 实际业务中不要使用unwrap
    // if let Ok(client) = LogClient::get() {
    //     client.push(LogLevel::Info, "server is runnig".into()).await;
    // }
    //lc.push(LogLevel::Error, "server had halted".into()).await;

    //receive ctrl-c exit signal
    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = svr.on_leave();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder()
        .add_service(AlgorithmCenterServer::new(svr))
        .serve_with_shutdown(addr, async {
            rx.await.ok();
        })
        .await
        .expect("build server error");

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    log_debug(&format!("phoenix_algorithmcenter server stopped")).await;
    Ok(())
}
