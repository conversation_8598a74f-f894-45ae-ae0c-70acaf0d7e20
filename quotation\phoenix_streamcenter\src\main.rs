#[macro_use]
extern crate anyhow;
extern crate chrono;

mod client;
mod config;
mod protofiles;
mod server;
use crate::config::settings::Settings;
use crate::protofiles::market_data_servers_server::MarketDataServersServer;
use crate::server::server::StreamServerHandler;
use anyhow::Result;
use common::init_tracing;
use common::logclient::init_logclient;
use tracing::info;
#[tokio::main]
async fn main() -> Result<()> {
    let prefix = "phoenix_streamcenter";
    let dir = "./log";

    let settings = Settings::new().expect("read config error");
    let level = "INFO";
    let _guard = init_tracing(prefix, dir, level);
    info!("初始化配置信息:{:?}", &settings);

    // 1. 日志中心客户端初始化
    init_logclient(&settings.system.logserver, &format!("phoenix_streamcenter")).await;

    let server = StreamServerHandler::new(&settings).await;

    let _ = run_server(server).await.unwrap();

    Ok(())
}

async fn run_server(mut server: StreamServerHandler) -> Result<(), Box<dyn std::error::Error>> {
    let app_url = format!("{}:{}", server.settings.application.apphost, server.settings.application.appport);
    let addr = app_url.as_str().parse().unwrap();

    info!("Starting streamcenter service on: {}", addr);
    info!("name: {} version: {} description: {}", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"), env!("CARGO_PKG_DESCRIPTION"));

    let (tx, rx) = tokio::sync::oneshot::channel::<()>();
    let on_leave = server.on_leave();

    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.ok();
        info!("Ctrl-c received, shutting down");
        tx.send(()).ok();
    });

    tonic::transport::Server::builder() //创建可以配置[server]的新服务器生成器。
        .tcp_keepalive(Some(std::time::Duration::from_secs(60)))
        .add_service(MarketDataServersServer::new(server))
        .serve_with_shutdown(addr, async {
            //使用该服务器创建一个未来，在tokio的服务器上执行该服务器,并在接收到所提供的信号时关闭。
            rx.await.ok();
        })
        .await?;

    info!("Shutted down, wait for final clear");
    on_leave.leave().await;
    info!("Shutted down");
    Ok(())
}
