use akaclient::akaclient::{AkaCacheOption, AkaClient};
use anyhow::Result;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, oneshot};
use tonic::Response;
use tracing::*;

use common::logclient::log_error;
use protoes::hqmsg::YsHqInfo;
use protoes::phoenixtickcenter::phoenix_tick_center_server::PhoenixTickCenter;
use protoes::phoenixtickcenter::{LastPriceReq, LastPriceResp, TickReq, TickResp};

use super::controller::TickCenterController;
use super::service::prelude::TickService;
use crate::client::{cassandraclient::CassandraClient, marketdataclient::MarketDataclient, sledclient::SledClient, tickmqclient::TickMqClient};
use crate::commonutil::commonutil::CommonUtil;
use crate::config::settings::Settings;

type StubType = Arc<TickCenterController>;
// type StubType = TickCenterController;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

pub struct TickServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}

pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);
impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

impl TickServerHandler {
    pub async fn new(setting: &Settings) -> Result<TickServerHandler> {
        let mut persist_interval = tokio::time::interval(std::time::Duration::from_secs(setting.system.cassinterval as u64));

        //创建通道
        let (tx, mut rx) = mpsc::channel(32);
        let (tx_close, mut rx_close) = oneshot::channel(); //创建一次性通道,用于发送单个值

        let (tx_tick_mq, mut rx_tick_mq) = broadcast::channel::<YsHqInfo>(setting.system.channelcap as usize);
        let (tx_tick, mut rx_tick) = broadcast::channel::<YsHqInfo>(setting.system.channelcap as usize);
        let _do_rx_tick = tx_tick.subscribe();

        let mut opt = AkaCacheOption::default();
        //缓存
        opt.use_cache = true;
        opt.mq_uri = format!("{}{}", setting.rabbitmq.amqpaddr, setting.rabbitmq.msgvhost);
        opt.exchange = "notification_center".to_owned().clone();
        opt.routing_keys = "notification.aka.#,notification.settlement.*".to_string();
        let aka_client = AkaClient::init(setting.system.akaserver.clone(), &opt).await;

        //创建cassandra
        let arc_cassandra_client: Option<Arc<CassandraClient>>;
        if setting.cassandra.addr.is_empty() {
            arc_cassandra_client = None;
        } else {
            // let cassandra_client = CassandraClient::new(&setting.cassandra).await.expect("cassandra连接出错..");
            // arc_cassandra_client = Some(Arc::new(cassandra_client));
            match CassandraClient::new(&setting.cassandra).await {
                Ok(cassandra_client) => {
                    arc_cassandra_client = Some(Arc::new(cassandra_client));
                }
                Err(e) => {
                    error!("cassandra连接出错..{}", e);
                    log_error("cassandra连接出错..").await;
                    arc_cassandra_client = None;
                }
            }
        }

        info!("start to init local cache");
        let sledclient = SledClient::new(&setting.system.filepath);
        let ticks = sledclient.read_tick().await;
        let tick_ctl = Arc::new(TickService::new(Arc::new(aka_client)));
        tick_ctl.init_tick_cache(&ticks).await;
        drop(ticks);
        info!("local cache init successfully");

        let common = CommonUtil::new();
        common.init(&setting).await.expect("init time error");

        let arc_mq_client: Option<Arc<TickMqClient>>;
        if setting.rabbitmq.amqpaddr.is_empty() {
            arc_mq_client = None;
        } else {
            let mq_client = TickMqClient::new(
                &setting.rabbitmq.exchanger,
                &setting.rabbitmq.exchanger_delay,
                &format!("{}{}", setting.rabbitmq.amqpaddr, setting.rabbitmq.hqvhost),
            )
            .await;
            arc_mq_client = Some(Arc::new(mq_client));
        }

        let stub = TickCenterController {
            tick_ctl,
            common_util: Arc::new(common),
            cassandra_client: arc_cassandra_client,
            tick_mq: arc_mq_client,
            sledclient: Arc::new(sledclient),
            // ticks_cache: Arc::new(RwLock::new(Vec::new())),
        };

        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();
        let stub_persist = stub.clone();
        let stub_mq = stub.clone();

        let ret = TickServerHandler {
            stub: stub.clone(),
            settings: setting.clone(),
            task_dispacther: tx,
            set_close: Some(tx_close),
        };

        let mut makclient = MarketDataclient::new(&setting.system.quotationserver, &setting.system.exchangeno, tx_tick).await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        tokio::spawn(async move {
            retry_interval.tick().await;
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = makclient.do_subscribe_market_data().await {
                            error!("{:?}", err);
                            // let _ = makclient.retry_do_subscribe_market_data().await;
                        }
                    }
                }
            }
        });

        // let mut num_max = 0;
        // let mut atomic_tick = AtomicBool::new(false);
        tokio::spawn(async move {
            persist_interval.tick().await;
            loop {
                tokio::select! {
                    _ = persist_interval.tick() => {
                        let _ = stub_persist.persist_ticks().await;
                        // if *atomic_tick.get_mut(){
                        //     info!("管道最大积压: {}", num_max);
                        //     let ret = stub_persist.persist_ticks().await;
                        //     if ret.as_ref().is_ok(){
                        //         atomic_tick.store(false, Ordering::Relaxed);
                        //     }else{
                        //         error!("persist to cassandra error");
                        //     }
                        // }
                    }
                    // tick_task_cache = do_rx_tick.recv()=>{
                    //     if num_max < do_rx_tick.len() {
                    //         num_max = do_rx_tick.len();
                    //     }
                    //     if let Ok(mut tick) = tick_task_cache {
                    //         if let Ok(_) = stub_persist.insert_cache(&mut tick).await {
                    //             if !*atomic_tick.get_mut(){
                    //                 atomic_tick.store(true, Ordering::Relaxed); //数据已经更新,可以保存到cassandra
                    //             }
                    //         }
                    //     }
                    // }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    tick_task = rx_tick_mq.recv() => {
                        if let Ok(tick) = tick_task {
                            if tick.contract_no1 == "600000_XSHG" || tick.contract_no1 == "03690_XHKG" || tick.contract_no1 == "000001_XSHE" {
                                info!("exchange_id: {}, code: {}, time: {}", tick.exchange_id, tick.contract_no1, tick.tapidtstamp);
                            }
                            if let Err(err) = stub_mq.send_tick_to_mq(&tick).await {
                                error!("send to mq error {:?}", err);
                                log_error(&format!("send to mq error {:?}", err).as_str()).await;
                            }
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    may_task = rx.recv() => {//接收信息
                        // info!("task from client received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        task(stub_for_dispatch.clone()).await;
                    }
                    tick_task = rx_tick.recv() => {
                        if let Ok(mut tick) = tick_task {
                            //板块行情直接推送MQ,不需要接下来的处理
                            // if tick.exchange_id == "HS" || tick.exchange_id == "HK" || tick.exchange_id == "US" {
                            //     stub.tick_ctl.ticks_cache.insert(tick.contract_no1.clone(), tick.clone());
                            //     if let Err(err) = tx_tick_mq.send(tick) {
                            //         error!("send to mq channel error {:?}", err);
                            //         log_error(&format!("send to mq error {:?}", err).as_str()).await;
                            //     }
                            //     // if let Err(err) = stub.send_tick_to_mq(&tick).await {
                            //     //     error!("send to mq error {:?}", err);
                            //     // }
                            //     // info!("板块行情推送完成: {}", &tick.contract_no1);
                            //     continue;
                            // }
                            if let Ok(_) = stub.insert_ticks(&mut tick).await {
                                if let Err(err) = tx_tick_mq.send(tick) {
                                    error!("send to mq channel error {:?}", err);
                                    log_error(&format!("send to mq error {:?}", err).as_str()).await;
                                }
                                // if let Err(err) = stub.send_tick_to_mq(&tick).await {
                                //     error!("send to mq error {:?}", err);
                                // }
                            }
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        Ok(ret)
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl PhoenixTickCenter for TickServerHandler {
    async fn get_tick_hq(&self, request: tonic::Request<TickReq>) -> Result<tonic::Response<TickResp>, tonic::Status> {
        let req = request.into_inner();
        info!("request: {:?}", req);
        match self.stub.get_tick_hq(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => {
                let res = TickResp { tick_hq_info: vec![] };
                info!("返回query reset 结果：{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }

    async fn get_last_price(&self, request: tonic::Request<LastPriceReq>) -> Result<tonic::Response<LastPriceResp>, tonic::Status> {
        let req = request.into_inner();
        info!("request: {:?}", req);
        match self.stub.get_stock_last_price(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => Ok(Response::new(LastPriceResp::default())),
        }
    }
}
