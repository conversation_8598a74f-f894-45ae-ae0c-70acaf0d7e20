use anyhow::Result;
use std::pin::Pin;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::sync::{broadcast, mpsc, oneshot};
use tonic::Response;
use tracing::{error, info, warn};

use akaclient::akaclient::{AkaCacheOption, AkaClient};
use protoes::{
    hqmsg::YsHqInfo,
    phoenixklinecenter::{phoenix_kline_center_server::PhoenixKlineCenter, FenShiResp, KLineDataResp, KLineHqRequest},
};

use super::controller::TickCenterController;
use super::service::{common::KLineData, klineservice::KLineService, prelude::TickService};
use crate::client::{cassandraclient::CassandraClient, marketdataclient::MarketDataclient, sledclient::SledClient};
use crate::commonutil::commonutil::CommonUtil;
use crate::config::settings::Settings;

type StubType = Arc<TickCenterController>;
// type StubType = TickCenterController;
type ControllerAction = Box<dyn FnOnce(StubType) -> Pin<Box<dyn futures::Future<Output = ()> + Send>> + Send>;

#[allow(dead_code)]
pub struct CalcServerHandler {
    stub: StubType,
    pub settings: Settings,
    task_dispacther: mpsc::Sender<ControllerAction>,
    set_close: Option<oneshot::Sender<()>>,
}

#[allow(dead_code)]
pub struct ServerLeave(mpsc::Sender<ControllerAction>, oneshot::Sender<()>);

#[allow(dead_code)]
impl ServerLeave {
    pub async fn leave(self) {
        self.1.send(()).unwrap();
        self.0.closed().await;
    }
}

#[allow(dead_code)]
impl CalcServerHandler {
    pub async fn new(setting: &Settings) -> Result<CalcServerHandler> {
        //创建新的时间间隔，该时间间隔随时间间隔而变化,第一次滴答声立即结束
        let mut fill_interval = tokio::time::interval(std::time::Duration::from_secs(1_u64));
        let mut interval = tokio::time::interval(std::time::Duration::from_secs(setting.system.cassinterval as u64));

        //创建通道
        let (tx, mut rx) = mpsc::channel(32);
        let (tx_close, mut rx_close) = oneshot::channel(); //创建一次性通道,用于发送单个值

        let (tx_tick, mut rx_tick) = broadcast::channel::<YsHqInfo>(setting.system.channelcap as usize);

        let (tx_kline, mut rx_kline) = mpsc::channel::<KLineData>(204800);

        let mut opt = AkaCacheOption::default();
        opt.use_cache = true;
        opt.mq_uri = format!("{}{}", setting.rabbitmq.amqpaddr, setting.rabbitmq.msgvhost);
        opt.exchange = "notification_center".to_owned().clone();
        opt.routing_keys = "notification.aka.#,notification.settlement.*".to_string();
        let aka_client = AkaClient::init(setting.system.akaserver.clone(), &opt).await;

        //创建cassandra
        let arc_cassandra_client: Option<Arc<CassandraClient>>;
        if setting.cassandra.addr.is_empty() {
            arc_cassandra_client = None;
        } else {
            match CassandraClient::new(&setting.cassandra.addr, &setting.cassandra.username, &setting.cassandra.password, &setting.cassandra.namespace).await {
                Ok(cassandra_client) => {
                    arc_cassandra_client = Some(Arc::new(cassandra_client));
                }
                Err(e) => {
                    error!("cassandra连接出错..{}", e);
                    arc_cassandra_client = None;
                }
            }
        }
        info!("start to init local cache");
        let sledclient = SledClient::new(&setting.system.filepath);
        let tick_ctl = Arc::new(TickService::new(Arc::new(aka_client)));
        let common = CommonUtil::new();
        common.init(&setting).await.expect("init time error");

        //K线相关
        let kline = KLineService::new(tx_kline);
        let kline_ctl = Arc::new(kline);

        let fenshis = sledclient.read_fenshi().await;
        if setting.system.repairklineflag {
            info!("未保存的kline数据,开始修复...");
            let kline_map = sledclient.read_kline().await;
            if let Some(cassandra_client) = arc_cassandra_client.clone() {
                let kline_vec: Vec<crate::server::service::persistservice::CassandraKLineData> = kline_map.iter().map(|(_k, v)| v.clone()).collect();
                let _ = cassandra_client.execute_batch_k(&kline_vec).await;
            }
        }

        let stub = TickCenterController {
            tick_ctl,
            common_util: common,
            cassandra_client: arc_cassandra_client,
            sledclient: Arc::new(sledclient),
            //以下是K线相关
            // set_day: std::collections::HashSet::new(),
            fenshi: Arc::new(dashmap::DashMap::new()),
            kline_ctl,
            klines_cache: Arc::new(RwLock::new(Vec::new())),
            exchange_contract: Arc::new(dashmap::DashMap::new()),
        };
        stub.init_center_cache(&fenshis).await;
        info!("local cache init successfully");
        drop(fenshis);
        let stub = Arc::new(stub);
        let stub_for_dispatch = stub.clone();

        // let stub_clone = stub.clone();
        let stub_persist = stub.clone();
        let stub_fill = stub.clone();

        let ret = CalcServerHandler {
            stub: stub.clone(),
            settings: setting.clone(),
            task_dispacther: tx,
            set_close: Some(tx_close),
        };

        let mut makclient = MarketDataclient::new(&setting.system.quotationserver, &setting.system.exchangeno, tx_tick.clone()).await;
        let mut retry_interval = tokio::time::interval(std::time::Duration::from_secs(3));
        tokio::spawn(async move {
            retry_interval.tick().await; //系统启动, 等时间间隔后执行(不加, 先执行)
            loop {
                tokio::select! {
                    _ = retry_interval.tick() => {
                        if let Err(err) = makclient.do_subscribe_market_data().await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        // let atomic_kline = AtomicBool::new(false);
        // tokio::spawn(async move {
        //     interval.tick().await;
        //     loop {
        //         tokio::select! {
        //             _ = interval.tick() => {
        //                 // if *atomic_kline.get_mut() {
        //                 if atomic_kline.load(std::sync::atomic::Ordering::Relaxed) {
        //                     let ret = stub_persist.persist_klines().await;
        //                     if ret.as_ref().is_ok(){
        //                         atomic_kline.store(false, Ordering::Relaxed);
        //                     }else{
        //                         error!("persist to cassandra error");
        //                     }
        //                 }
        //             }
        //             kline_task = rx_kline.recv() => {
        //                 if let Some(kline) = kline_task {
        //                     // stub_clone.sledclient.file_cache(&kline).await;
        //                     if kline.period == 1 {//缓存分时数据
        //                         if let Err(e) = stub_clone.generate_time_line(&kline, true).await {
        //                             error!("cache error:{:?}", &e);
        //                         }
        //                     }
        //                     if let Err(e) = stub_clone.insert_cache_klines(&kline).await {
        //                         error!("insert cache error:{:?}", &e);
        //                     }
        //                     // if !*atomic_kline.get_mut() {
        //                     if !atomic_kline.load(std::sync::atomic::Ordering::Relaxed) {
        //                         atomic_kline.store(true, Ordering::Relaxed);
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // });

        let atomic_kline = Arc::new(AtomicBool::new(false));
        let stub_persist = stub_persist.clone();
        let atomic_kline_persist = atomic_kline.clone();
        tokio::spawn(async move {
            interval.tick().await;
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        if atomic_kline_persist.load(std::sync::atomic::Ordering::Relaxed) {
                            if let Ok(_) = stub_persist.persist_klines().await {
                                atomic_kline_persist.store(false, Ordering::Relaxed);
                            }
                        }
                    }
                }
            }
        });

        // tokio::spawn(async move {
        //     loop {
        //         tokio::select! {
        //             kline_task = rx_kline.recv() => {
        //                 if let Some(kline) = kline_task {
        //                     if kline.period == 1 {
        //                         if let Err(e) = stub_clone.generate_time_line(&kline, true).await {
        //                             error!("cache error:{:?}", &e);
        //                         }
        //                     }
        //                     if let Err(e) = stub_clone.insert_cache_klines(&kline).await {
        //                         error!("insert cache error:{:?}", &e);
        //                     }
        //                     if !atomic_kline.load(std::sync::atomic::Ordering::Relaxed) {
        //                         atomic_kline.store(true, Ordering::Relaxed);
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // });

        // use std::sync::Arc;
        // use tokio::sync::{Mutex, Notify};

        // let notify = Arc::new(Notify::new());
        // let notify_persist = notify.clone();
        // let stub_persist = stub_persist.clone();
        // let interval = interval.clone();

        // // 持久化任务
        // tokio::spawn(async move {
        //     let mut interval = interval;
        //     interval.tick().await;
        //     loop {
        //         interval.tick().await;
        //         notify_persist.notified().await; // 等待通知
        //         if let Err(e) = stub_persist.persist_klines().await {
        //             error!("persist to cassandra error: {:?}", e);
        //         } else {
        //             info!("persist to cassandra success");
        //         }
        //     }
        // });

        // let stub_clone = stub_clone.clone();
        // let rx_kline = rx_kline;
        // let notify_kline = notify.clone();

        // // k线消息处理任务
        // tokio::spawn(async move {
        //     let mut rx_kline = rx_kline;
        //     while let Some(kline) = rx_kline.recv().await {
        //         if kline.period == 1 {
        //             if let Err(e) = stub_clone.generate_time_line(&kline, true).await {
        //                 error!("cache error:{:?}", &e);
        //             } else {
        //                 info!("cache success");
        //             }
        //         }
        //         if let Err(e) = stub_clone.insert_cache_klines(&kline).await {
        //             error!("insert cache error:{:?}", &e);
        //         } else {
        //             info!("insert cache success");
        //         }
        //         // 通知持久化任务
        //         notify_kline.notify_one();
        //     }
        // });

        tokio::spawn(async move {
            fill_interval.tick().await;
            loop {
                tokio::select! {
                    _ = fill_interval.tick() => {//定时任务(每秒执行一次)
                        let _ = stub_fill.fill().await;
                        if let Err(err) = stub_fill.persist_fenshi().await {
                            error!("{:?}", err);
                        }
                    }
                }
            }
        });

        tokio::spawn(async move {
            //在到达间隔中的下一个瞬间时完成。
            // fill_interval.tick().await;
            loop {
                tokio::select! {
                    may_task = rx.recv() => {//接收信息
                        info!("task from client received...");
                        let task = may_task.expect("Server scheduler has unexpected exit");
                        task(stub_for_dispatch.clone()).await;
                    }
                    kline_task = rx_kline.recv() => {
                        if let Some(kline) = kline_task {
                            if kline.period == 1 {
                                if let Err(e) = stub.generate_time_line(&kline, true).await {
                                    error!("cache error:{:?}", &e);
                                }
                            }
                            if let Err(e) = stub.insert_cache_klines(&kline).await {
                                error!("insert cache error:{:?}", &e);
                            }
                            if !atomic_kline.load(std::sync::atomic::Ordering::Relaxed) {
                                atomic_kline.store(true, Ordering::Relaxed);
                            }
                        }
                    }
                    tick_task = rx_tick.recv() => {
                        if let Ok(mut tick) = tick_task{
                            if tick.exchange_id == "HS" || tick.exchange_id == "HK" || tick.exchange_id == "US" {
                                //不用处理
                                continue;
                            }
                            if let Err(_) =  stub.insert_ticks(&mut tick).await {
                                // error!("invalid ticks received... {:?}", err);
                            } else {
                                if let Err(err) = stub.deal_tick_range(&tick).await {
                                    error!("{:?}", err);
                                }
                            }
                        }
                    }
                    _ = &mut rx_close => {
                        info!("Server scheduler is notified to close");
                        rx.close();
                        break;
                    }
                }
            }

            while let Some(task) = rx.recv().await {
                task(stub_for_dispatch.clone()).await;
                info!("drain unhandled task received");
            }

            warn!("Server scheduler has exited");
        });

        Ok(ret)
    }

    pub fn on_leave(&mut self) -> ServerLeave {
        ServerLeave(self.task_dispacther.clone(), self.set_close.take().expect("Do not call twice with on_leave"))
    }
}

#[tonic::async_trait]
impl PhoenixKlineCenter for CalcServerHandler {
    async fn get_last_kline_data(&self, request: tonic::Request<KLineHqRequest>) -> Result<tonic::Response<KLineDataResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        match self.stub.get_last_kline_data(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => {
                let res = KLineDataResp::default();
                info!("query reset：{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }
    async fn get_generate_fenshi_hq(&self, request: tonic::Request<KLineHqRequest>) -> Result<tonic::Response<FenShiResp>, tonic::Status> {
        info!("client connected from:{:?}", &request.remote_addr());
        let req = request.into_inner();
        match self.stub.get_generate_fenshi_hq(&req).await {
            Ok(res) => Ok(Response::new(res)),
            Err(_err) => {
                let res = FenShiResp::default();
                info!("query reset：{:?}", &res);
                Ok(Response::new(res))
            }
        }
    }
}
