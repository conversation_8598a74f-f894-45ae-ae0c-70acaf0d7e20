extern crate chrono;
use anyhow::Result;
use chrono::prelude::*;
// use prost::Message;
use std::fmt::Write;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::*;
// use scylla::IntoTypedRows;

use protoes::{
    hqcenter::{KLineHqInfo, KLineHqReq, KLineHqResp, LastPriceInfo, LastPriceMsgReq, LastPriceMsgResp, PreDealAmount, PreDealNumReq, ResultMsg, SubscribeHqMsgReq, TickHqReq, TickHqResp},
    hqmsg::YsHqInfo,
};
use utility::timeutil::{build_naive_date_time, current_naive_time, from_timestamp_to_naive_date_time};

use super::service::common::{HqTick, KlineDate};
use crate::client::{cassandraclient::CassandraClient, klinecenterclient::KlineCenterClient, tickcenterclient::TickCenterClient, usmartclient::UsMartClient};
use crate::config::settings::Settings;
use crate::hqerror::hqerrors;
use crate::server::service::common::{anti_serialize_tick, get_current_period, get_table_name_by_kline_type, KLineType};

//行情查询控制中心
#[derive(Clone)]
pub struct PhoenixHqcenterController {
    pub settings: Settings,
    pub tick_client: Arc<RwLock<TickCenterClient>>,
    pub kline_client: Arc<RwLock<KlineCenterClient>>,
    pub us_client: Arc<RwLock<UsMartClient>>,
    pub cassandra_client: Arc<CassandraClient>,
}

impl PhoenixHqcenterController {
    //行情订阅
    pub async fn get_post_subscribe_hq_msg(&self, _req: &SubscribeHqMsgReq) -> Result<ResultMsg> {
        let res = ResultMsg {
            err_msg: hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).0,
            err_code: hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).1,
        };
        Ok(res)
    }

    //查询K线
    pub async fn get_hq_kline(&self, req: &KLineHqReq) -> Result<KLineHqResp> {
        info!("strcontractno: {}, realtime: {}, klinetype: {}", req.strcontractno, req.realtime, req.strklinetype);
        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();
        let mut klinedate = KlineDate::default();

        if req.strcontractno.contains("_XASE") || req.strcontractno.contains("_XNYS") || req.strcontractno.contains("_XNAS") || req.strcontractno.contains("_US") {
            if let Ok(kline) = self.us_client.write().await.query_us_kline(req).await {
                return Ok(kline);
            } else {
                return Ok(resp);
            }
        }
        // if req.strklinetype == "168" || req.strklinetype == "720" {
        //     // 1.cassandra中没有当前周期的 内存+Cassandra
        //     // 2.Cassandra中有当前周期的 合并内存与Cassandra第一条+Cassandra其他条
        //     PhoenixHqcenterController::get_multiple_day_kline(&self, &req, &mut resp).await;
        //     resp.klineinfo.push(kline_info);
        //     return Ok(resp);
        // }

        let turnover: f64 = Default::default();
        let filter_num: i32 = Default::default();

        let mut kline_type: i32 = req.strklinetype.parse().unwrap_or_default(); //行情类型由字符串类型解析为i32类型
        if kline_type == 24 {
            kline_type = KLineType::Hq24Kline as i32; //1440
        }
        let table_name = get_table_name_by_kline_type(kline_type).await; //根据K线类型获取表名
        info! {"获取表:{}", table_name};
        let mut flag: bool = true; //是否从内存中取最新K线
        let mut btime: i64 = Default::default();
        let mut etime: i64 = Default::default();
        // let exchange: i32 = Default::default();

        //时间相关
        let start_time: i64 = Default::default();
        let end_time: i64 = Default::default();
        let tsecond: i64 = Default::default();
        let nextperiod_time: i64 = Default::default();

        //查询处理
        if !table_name.is_empty() {
            // //港股
            // if req.strcontractno.contains("XHKG") {
            //     //contains查找子串,匹配返回true
            //     exchange = 103;
            // }

            // //不是沪深，延迟行情
            // if !req.strcontractno.contains("XSHE") && !req.strcontractno.contains("XSHG") {
            //     if req.realtime == 0 {//延时行情
            //         info!("延时行情......");
            //         let mut iper_time: i64 = Default::default();
            //         let mut st = current_naive_time();
            //         let mut tt = st.timestamp();
            //         info!("tt: {}", tt);
            //         tsecond = tt - 15 * 60;
            //         info!("timestamp: {}", tsecond);
            //         if exchange == 103 {
            //             tt = tt - 15 * 60;
            //             info!("exchange == 103, tt:{}", tt);
            //         } else {
            //             if self.settings.common.winter_time != 1 {
            //                 //夏令时
            //                 tt = tt - 15 * 60 - 12 * 3600;
            //                 info!("[get_hq_kline] 夏令时 tt:{}", tt);
            //             } else {
            //                 //冬令时差相差13个小时
            //                 tt = tt - 15 * 60 - 13 * 3600;
            //                 info!("[get_hq_kline] 冬令时 tt:{}", tt);
            //             }
            //         }
            //         st = from_timestamp_to_naive_date_time(tt);
            //         if st.hour() >= 9 {
            //             //上一周期结束的时间
            //             iper_time = ((st.hour() as i64) * 60 + (st.minute() as i64) - 9 * 60 - 30) % (kline_type as i64);
            //         }
            //         let ret = convert_from_naivdate_i64(tt).await;
            //         if ret.as_ref().is_err() {
            //             info!("error: {:?}", &ret);
            //             return Err(anyhow!("{}", &ret.as_ref().err().unwrap().to_string()));
            //         }
            //         end_time = ret.unwrap() * 100;
            //         info! {"[get_hq_kline] K线类型:{}  st.hour:{} end_time:{}",kline_type, st.hour(), end_time};
            //         info!("[get_hq_kline] 上一周期结束的时间:{}", iper_time);

            //         tt = tt - iper_time * 60;

            //         let ret = convert_from_naivdate_i64(tt).await;
            //         if ret.as_ref().is_err() {
            //             info!("error: {}", ret.as_ref().is_err());
            //             return Err(anyhow!("{}", ret.as_ref().err().unwrap().to_string()));
            //         }
            //         start_time = ret.unwrap() * 100;
            //         info! {"[get_hq_kline]  start_time:{}",start_time};
            //         //下一周期结束时间

            //         if kline_type < KLineType::Hq24Kline as i32 {
            //             tt = tt + (kline_type as i64) * 60;
            //             info!("[get_hq_kline] ？tt:{}", tt);
            //         }
            //         if exchange != 103 {
            //             if self.settings.common.winter_time != 1 {
            //                 tt = tt + 12 * 3600; //夏令时
            //                 info!("[get_hq_kline] 夏令时ts:{}", tt);
            //             } else {
            //                 tt = tt + 13 * 3600; //冬令时相差13个小时
            //                 info!("[get_hq_kline] 冬令时ts:{}", tt);
            //             }
            //         }
            //         let ret = convert_from_naivdate_i64(tt).await;
            //         if ret.as_ref().is_err() {
            //             info!("error: {:?}", &ret);
            //             return Err(anyhow!("{}", &ret.as_ref().err().unwrap().to_string()));
            //         }
            //         nextperiod_time = ret.unwrap() * 100;
            //         info! {"[get_hq_kline] K线类型:{}, nextperiod_time:{}",kline_type, nextperiod_time};
            //         filter_num = 1;
            //     }
            // }

            info!("[get_hq_kline] filterNum: {}", filter_num);
            let strtime = req.strendtime.clone();
            info!("[get_hq_kline] strtime:{}", strtime);
            if !strtime.is_empty() {
                //返回true时为空
                if !strtime.contains("-") {
                    //查找字符串，找到返回true
                    btime = strtime.parse().unwrap_or_default();
                    info!("btime:{}", btime);
                } else {
                    let time: Vec<&str> = strtime.split("-").collect(); //分割字符串，collect() 方法将迭代器转换为 向量 Vector
                    btime = time[0].parse().unwrap_or_default();
                    etime = time[1].parse().unwrap_or_default();
                    info!("[get_hq_kline] btime:{} etime:{}", btime, etime);
                    flag = false;
                }
            }

            if filter_num == 1 {
                //港股延时
                //Query1kline
                PhoenixHqcenterController::query_1_kline(&self, req, &mut resp.klineinfo, end_time, start_time, tsecond, nextperiod_time)
                    .await
                    .unwrap();
            } else {
                if flag {
                    //接口请求
                    //从内存中获取行情最新K线
                    let ret = self.get_last_kline_data(&req.strcontractno, &req.strklinetype).await;
                    if ret.as_ref().is_err() {
                        error!("{:?}", &ret);
                        return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                    }
                    let last_kline_value = ret.unwrap();
                    if !last_kline_value.is_empty() {
                        info!("{}最新{}分钟K线: {}", req.strcontractno, kline_type, last_kline_value);
                        kline_info.strkline = last_kline_value;
                        resp.klineinfo.push(kline_info.clone());
                    }
                }
            }

            let mut cql: String = String::default();
            if btime == 0 && etime == 0 {
                //req.end_time = ""
                cql = format!(
                    "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
                    from {} where vc_code = '{}' order by l_update_time desc limit {} ",
                    table_name.to_string(),
                    req.strcontractno.clone(),
                    req.limit.to_string()
                );
            } else if btime != 0 && etime == 0 {
                //req.end_time = "20220817133300"
                cql = format!(
                    "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
                    from {} where vc_code = '{}' and l_update_time > {} order by l_update_time desc",
                    table_name.to_string(),
                    req.strcontractno.clone(),
                    btime
                );
            } else if btime != 0 && etime != 0 {
                //req.end_time = "20220816133300-20220816134000"
                //不取最新的
                cql = format!(
                    "select l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
                    from {} where vc_code = '{}' and l_update_time > {} and l_update_time < {} order by l_update_time desc",
                    table_name.to_string(),
                    req.strcontractno.clone(),
                    btime,
                    etime
                );
            }

            info!("cql: {}", cql);

            let query_ret = self.cassandra_client.query_cass(&cql, &req.strcontractno).await;
            if let Some(rows_result) = query_ret {
                let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
                while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                    klinedate.l_update_time = l_update_time;
                    klinedate.en_close_price = en_close_price;
                    klinedate.en_high_price = en_high_price;
                    klinedate.en_last_price = en_last_price;
                    klinedate.en_low_price = en_low_price;
                    klinedate.en_open_price = en_open_price;
                    klinedate.l_volume = l_volume;

                    if filter_num == 1 {
                        //延迟需要过滤
                        if klinedate.l_update_time > end_time {
                            continue; //直接跳过
                        }
                    }

                    klinedate.l_update_time -= klinedate.l_update_time % 100; //去掉秒
                    if kline_type != KLineType::Hq24Kline as i32 {
                        let ret = PhoenixHqcenterController::convert_to_local_time(&self, klinedate.l_update_time).await;
                        if ret.as_ref().is_err() {
                            error!("{:?}", &ret);
                            return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                        }
                        klinedate.l_update_time = ret.unwrap();
                    }
                    //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
                    //保留3位小数
                    let kline_value = format!(
                        "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                        klinedate.en_close_price, klinedate.en_high_price, klinedate.en_last_price, klinedate.en_low_price, klinedate.en_open_price, klinedate.l_volume, klinedate.l_update_time, turnover
                    );
                    kline_info.strkline = kline_value;
                    resp.klineinfo.push(kline_info.clone());
                }

                // for row in rows.rows::<(i64, f32, f32, f32, f32, f32, i32)>() {
                //     match row {
                //         Ok(data) => {
                //             klinedate.l_update_time = data.0;
                //             klinedate.en_close_price = data.1;
                //             klinedate.en_high_price = data.2;
                //             klinedate.en_last_price = data.3;
                //             klinedate.en_low_price = data.4;
                //             klinedate.en_open_price = data.5;
                //             klinedate.l_volume = data.6;

                //             if filter_num == 1 {
                //                 //延迟需要过滤
                //                 if klinedate.l_update_time > end_time {
                //                     continue; //直接跳过
                //                 }
                //             }

                //             klinedate.l_update_time -= klinedate.l_update_time % 100; //去掉秒
                //             if kline_type != KLineType::Hq24Kline as i32 {
                //                 let ret = PhoenixHqcenterController::convert_to_local_time(&self, klinedate.l_update_time).await;
                //                 if ret.as_ref().is_err() {
                //                     error!("{:?}", &ret);
                //                     return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                //                 }
                //                 klinedate.l_update_time = ret.unwrap();
                //             }
                //             //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
                //             //保留3位小数
                //             let kline_value = format!(
                //                 "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                //                 klinedate.en_close_price, klinedate.en_high_price, klinedate.en_last_price, klinedate.en_low_price, klinedate.en_open_price, klinedate.l_volume, klinedate.l_update_time, turnover
                //             );
                //             kline_info.strkline = kline_value;
                //             resp.klineinfo.push(kline_info.clone());
                //         }
                //         Err(err) => {
                //             error!("{:?}", err);
                //             return Ok(resp);
                //         }
                //     }
                // }
            } else {
                info!("Query Cassandra None");
            }

            info!("[get_hq_kline] GetHqKline kline info size:{}", resp.klineinfo.len());
        } else {
            info!("[get_hq_kline] K线请求信息有误: code: {}, type: {}", &req.strcontractno, &req.strklinetype);
        }
        info!("get_hq_kline End");
        let mut seen = std::collections::HashSet::new();
        let mut result = Vec::new();

        for item in resp.klineinfo {
            // 如果该元素是唯一的，则将其添加到结果中
            let str: Vec<&str> = item.strkline.rsplitn(3, "|").collect();
            // info!("str:{}", str[1]);
            // 20241126150000
            if seen.insert(str[1].to_owned()) {
                // 如果插入成功，说明该元素是唯一的
                result.push(item.clone()); // 将其添加到结果中
            }
        }
        resp.klineinfo = result;
        Ok(resp)
    }

    // 1.cassandra中没有当前周期的 内存+Cassandra
    // 2.Cassandra中有当前周期的 合并内存与Cassandra第一条+Cassandra其他条
    #[allow(dead_code)]
    async fn get_multiple_day_kline(&self, req: &KLineHqReq, resp: &mut KLineHqResp) -> Result<()> {
        info!("[get_multiple_day_kline] code: {}, type: {}", req.strcontractno, req.strklinetype);
        let mut klinedate = KlineDate::default();
        let mut kline_info = KLineHqInfo::default();
        let mut buffer: String; //= String::default();
        let period: i32 = req.strklinetype.parse().unwrap_or_default(); //K线类型
                                                                        //获取当前期间
        let _current_period = get_current_period(period, &req.strcontractno).await;

        // let mut flag: bool; // = false;

        //从内存中获取最新日K线
        let mut kline_client = self.kline_client.write().await;
        let ret_kline = kline_client.get_last_kline_data(&req.strcontractno, &req.strklinetype).await;
        if ret_kline.as_ref().is_err() {
            error!("{:?}", &ret_kline);
            drop(kline_client);
            return Err(anyhow!("{:?}", &ret_kline.as_ref().err().unwrap().to_string()));
        }
        drop(kline_client);
        let kline_data = ret_kline.unwrap();
        if !kline_data.stock_code.is_empty() {}

        //接下来从Cassandra中取
        {
            let table_name = get_table_name_by_kline_type(period).await; //根据K线类型获取表名
            if !table_name.is_empty() {
                // let mut l_date: i32 = Default::default();
                let cql = format!(
                    "select l_update_time, en_close_price,  en_high_price, en_last_price, en_low_price, en_open_price, l_volume from {} where vc_code = '{}' order by l_update_time desc limit {};",
                    table_name, req.strcontractno, req.limit
                );
                info!("get_multiple_day_kline query cql: {}", cql);

                let query_ret = self.cassandra_client.query_cass(&cql, &req.strcontractno).await;
                if let Some(rows_result) = query_ret {
                    let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
                    while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                        klinedate.l_update_time = l_update_time;
                        klinedate.en_close_price = en_close_price;
                        klinedate.en_high_price = en_high_price;
                        klinedate.en_last_price = en_last_price;
                        klinedate.en_low_price = en_low_price;
                        klinedate.en_open_price = en_open_price;
                        klinedate.l_volume = l_volume;

                        buffer = format!(
                            "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                            klinedate.en_close_price, klinedate.en_high_price, klinedate.en_last_price, klinedate.en_low_price, klinedate.en_open_price, klinedate.l_volume, klinedate.l_update_time, 0.00
                        );
                        info!("get_multiple_day_kline {}({}) {}", req.strcontractno.clone(), period, buffer);
                        kline_info.strkline = buffer;
                        resp.klineinfo.push(kline_info.clone());
                    }
                    // for row in rows.into_typed::<(i64, f32, f32, f32, f32, f32, i32)>() {
                    //     match row {
                    //         Ok(data) => {
                    //             klinedate.l_update_time = data.0;
                    //             klinedate.en_close_price = data.1;
                    //             klinedate.en_high_price = data.2;
                    //             klinedate.en_last_price = data.3;
                    //             klinedate.en_low_price = data.4;
                    //             klinedate.en_open_price = data.5;
                    //             klinedate.l_volume = data.6;

                    //             buffer = format!(
                    //                 "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    //                 klinedate.en_close_price, klinedate.en_high_price, klinedate.en_last_price, klinedate.en_low_price, klinedate.en_open_price, klinedate.l_volume, klinedate.l_update_time, 0.00
                    //             );
                    //             info!("get_multiple_day_kline {}({}) {}", req.strcontractno.clone(), period, buffer);
                    //             kline_info.strkline = buffer;
                    //             resp.klineinfo.push(kline_info.clone());
                    //         }
                    //         Err(err) => {
                    //             error!("{:?}", err);
                    //             return Ok(());
                    //         }
                    //     }
                    // }
                }
            }
        }
        Ok(())
    }

    async fn query_1_kline(&self, req: &KLineHqReq, klineinfo: &mut Vec<KLineHqInfo>, et: i64, st: i64, tsecond: i64, nextperiod_time: i64) -> Result<()> {
        info!("[query_1_kline] start_time:{} end_time:{}", st, et);

        let mut klinedate = KlineDate::new().await;
        let d_turnover: f64 = Default::default();

        let mut close_price: f32 = Default::default();
        let mut high_price: f32 = Default::default();
        let mut last_price: f32 = Default::default();
        let mut low_price: f32 = Default::default();
        let mut open_price: f32 = Default::default();
        let mut update_time: i64 = Default::default();
        let mut volume: i32 = Default::default();

        let mut turnover: f64 = Default::default();

        let mut cql = format!(
            " select l_update_time, en_close_price,  en_high_price, en_last_price, en_low_price, en_open_price, l_volume \
                from tstockhq1kline where vc_code = '{}' and l_update_time <= {} and l_update_time > {} order by l_update_time desc ;",
            req.strcontractno.clone(),
            et,
            st
        );
        info!("[query_1_kline] cql: {}", cql);

        let mut size: i32 = 0;
        let query_ret = self.cassandra_client.query_cass(&cql, &req.strcontractno).await;
        if let Some(rows_result) = query_ret {
            let mut rows_iter = rows_result.rows::<(i64, f32, f32, f32, f32, f32, i32)>()?;
            while let Some((l_update_time, en_close_price, en_high_price, en_last_price, en_low_price, en_open_price, l_volume)) = rows_iter.next().transpose()? {
                klinedate.l_update_time = l_update_time;
                klinedate.en_close_price = en_close_price;
                klinedate.en_high_price = en_high_price;
                klinedate.en_last_price = en_last_price;
                klinedate.en_low_price = en_low_price;
                klinedate.en_open_price = en_open_price;
                klinedate.l_volume = l_volume;

                if size == 0 {
                    close_price = klinedate.en_close_price;
                    high_price = klinedate.en_high_price;
                    last_price = klinedate.en_last_price;
                    low_price = klinedate.en_low_price;
                    open_price = klinedate.en_open_price;
                    turnover = d_turnover;
                    update_time = klinedate.l_update_time;
                    volume = klinedate.l_volume;
                } else {
                    volume += klinedate.l_volume;
                    turnover += d_turnover;
                    if klinedate.en_high_price > high_price {
                        high_price = klinedate.en_high_price;
                    }
                    if klinedate.en_low_price < low_price {
                        low_price = klinedate.en_low_price;
                    }
                }
                size += 1;
            }
            // for row in rows.into_typed::<(i64, f32, f32, f32, f32, f32, i32)>() {
            //     match row {
            //         Ok(data) => {
            //             klinedate.l_update_time = data.0;
            //             klinedate.en_close_price = data.1;
            //             klinedate.en_high_price = data.2;
            //             klinedate.en_last_price = data.3;
            //             klinedate.en_low_price = data.4;
            //             klinedate.en_open_price = data.5;
            //             klinedate.l_volume = data.6;

            //             if size == 0 {
            //                 close_price = klinedate.en_close_price;
            //                 high_price = klinedate.en_high_price;
            //                 last_price = klinedate.en_last_price;
            //                 low_price = klinedate.en_low_price;
            //                 open_price = klinedate.en_open_price;
            //                 turnover = d_turnover;
            //                 update_time = klinedate.l_update_time;
            //                 volume = klinedate.l_volume;
            //             } else {
            //                 volume += klinedate.l_volume;
            //                 turnover += d_turnover;
            //                 if klinedate.en_high_price > high_price {
            //                     high_price = klinedate.en_high_price;
            //                 }
            //                 if klinedate.en_low_price < low_price {
            //                     low_price = klinedate.en_low_price;
            //                 }
            //             }
            //             size += 1;
            //         }
            //         Err(err) => {
            //             error!("{:?}", err);
            //             return Ok(());
            //         }
            //     }
            // }
            //去掉秒
            update_time -= update_time % 100;
            info!("[query_1_kline] update_time:{}", update_time);
            if size != 0 {
                //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
                let kline_value = format!(
                    "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    close_price, high_price, last_price, low_price, open_price, volume, nextperiod_time, turnover
                );
                info!("[query_1_kline] sklineValue:{}", kline_value);
                let klinehqinfo = KLineHqInfo { strkline: kline_value };
                klineinfo.push(klinehqinfo);
            }
            info!("[query_1_kline] size:{}", klineinfo.len());
        } else {
            info!("Query Cassandra None");
        }

        let mut tick = HqTick::new(); //cassandra tick value
                                      //tstockhqtick
        cql = format!(
            "select vc_content from tstockhqtick where vc_contract_code = '{}' \
            and l_update_time <= '{}' and l_update_time > '{}' order by l_update_time desc ;",
            req.strcontractno.clone(),
            tsecond * 1000,
            (tsecond + 60) * 1000
        );
        info!("cql: {}", cql);

        let query_ret = self.cassandra_client.query_cass(&cql, &req.strcontractno).await;
        if let Some(rows_result) = query_ret {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            while let Some(vc_content) = rows_iter.next().transpose()? {
                tick.vc_content = vc_content.0;
                let mut hqtick = YsHqInfo::default();
                anti_serialize_tick(&mut hqtick, &tick.vc_content).await;
                info!("{:#?}", hqtick);
                {
                    volume += hqtick.q_last_qty as i32;
                    turnover += 0.0;
                    if (hqtick.q_high_price as f32) > high_price {
                        high_price = hqtick.q_high_price as f32;
                    }
                    if (hqtick.q_low_price as f32) < low_price {
                        low_price = hqtick.q_low_price as f32;
                    }
                }
                size = size + 1;
            }
            // for row in rows.into_typed::<(String,)>() {
            //     match row {
            //         Ok((vc_content,)) => {
            //             tick.vc_content = vc_content;
            //             let mut hqtick = YsHqInfo::default();
            //             anti_serialize_tick(&mut hqtick, &tick.vc_content).await;
            //             info!("{:#?}", hqtick);
            //             {
            //                 volume += hqtick.q_last_qty as i32;
            //                 turnover += 0.0;
            //                 if (hqtick.q_high_price as f32) > high_price {
            //                     high_price = hqtick.q_high_price as f32;
            //                 }
            //                 if (hqtick.q_low_price as f32) < low_price {
            //                     low_price = hqtick.q_low_price as f32;
            //                 }
            //             }
            //             size = size + 1;
            //         }
            //         Err(err) => {
            //             error!("{:?}", err);
            //             return Ok(());
            //         }
            //     }
            // }
            //去掉秒
            if size != 0 {
                //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
                let kline_value = format!(
                    "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
                    close_price, high_price, last_price, low_price, open_price, volume, nextperiod_time, turnover
                );
                info!("[query_1_kline] sklineValue:{}", kline_value);
                let klinehqinfo = KLineHqInfo { strkline: kline_value };
                klineinfo.push(klinehqinfo);
            }
            info!("[query_1_kline] size:{}", klineinfo.len());
        } else {
            info!("Query Cassandra None");
        }

        info!("[query_1_kline] end");

        Ok(())
    }

    pub async fn get_current_fenshi_hq(&self, req: &KLineHqReq) -> Result<KLineHqResp> {
        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();
        if req.strcontractno.contains("_XASE") || req.strcontractno.contains("_XNYS") || req.strcontractno.contains("_XNAS") || req.strcontractno.contains("_US") {
            if let Ok(kline) = self.us_client.write().await.query_us_fen_shi(&req.strcontractno).await {
                kline_info.strkline = kline.clone();
                resp.klineinfo.push(kline_info);
                return Ok(resp);
            } else {
                return Ok(resp);
            }
        }
        //已经生成的
        let ret = self.kline_client.write().await.get_generate_fenshi_hq(&req.strcontractno, &req.strklinetype).await;
        if ret.as_ref().is_err() {
            error!("{:?}", &ret);
            return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
        }
        let mut val_resp = ret.unwrap().fenshi_hq.clone();
        info!("已经生成的分时: {}", &val_resp);

        //再从内存中拿一份当前未生成的(1分钟)
        let ret_kline = self.kline_client.write().await.get_last_kline_data(&req.strcontractno, &1.to_string()).await;
        if ret_kline.as_ref().is_err() {
            error!("{:?}", &ret_kline);
            return Err(anyhow!("{:?}", &ret_kline.as_ref().err().unwrap().to_string()));
        }
        let kline_data = ret_kline.unwrap();
        if kline_data.stock_code.is_empty() {
            info!("未找到最新K线数据: {:?}", &kline_data);
            if val_resp.is_empty() {
                //如果没有值从cassandra取
                let ret = self.get_history_time_share(&req.strcontractno).await;
                if ret.as_ref().is_err() {
                    error!("{:?}", &ret);
                    return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                }
                resp = ret.unwrap();
            } else {
                kline_info.strkline = val_resp.clone();
                resp.klineinfo.push(kline_info);
            }
            return Ok(resp);
        }

        let minutes = (kline_data.prev_minutes + kline_data.period) % 1440;
        let st = build_naive_date_time(&kline_data.tick_time);
        //年月日时分
        let time = format!("{:04}{:02}{:02}{:02}{:02}00", &st.year(), &st.month(), &st.day(), minutes / 60, minutes % 60);

        if !val_resp.is_empty() {
            let ret: Vec<&str> = val_resp.rsplit('|').collect();
            info!("分时线日期: {}", ret[0][0..8].to_string());
            if time.contains(&ret[0][0..8].to_string()) {
                let delta_data = format!("{:<.03}|{:<.03}|{}|{}+", kline_data.average_price, kline_data.last_price, kline_data.current_period_volume, time);
                let _ = write!(val_resp, "{}", delta_data);
            }
        } else {
            let delta_data = format!(
                "{:<.03}|{:<.03}|{:<.03}|{}|{}+",
                kline_data.pre_close_price, kline_data.average_price, kline_data.last_price, kline_data.current_period_volume, time
            );
            val_resp = delta_data;
        }
        info!("分时: {}", &val_resp);

        if val_resp.is_empty() {
            //如果没有值从cassandra取
            let ret = self.get_history_time_share(&req.strcontractno).await;
            if ret.as_ref().is_err() {
                error!("{:?}", &ret);
                return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
            }
            resp = ret.unwrap();
        } else {
            kline_info.strkline = val_resp.clone();
            resp.klineinfo.push(kline_info);
        }
        Ok(resp)
    }

    pub async fn get_pre_deal_amount(&self, req: &PreDealNumReq) -> Result<PreDealAmount> {
        info!("get_pre_deal_amount StockCode:{} ExchangeId:{}", req.stock_code, req.exchange_id);
        let mut res = PreDealAmount::default();

        let exchange_id: i32 = req.exchange_id;
        let contract_no1 = match exchange_id {
            101 => req.stock_code.clone() + "_XSHG",
            102 => req.stock_code.clone() + "_XSHE",
            103 => req.stock_code.clone() + "_XHKG", //港交所
            104 => req.stock_code.clone() + "_XASE", //美交所
            105 => req.stock_code.clone() + "_XNYS", //纽交所
            106 => req.stock_code.clone() + "_XNAS", //纳斯达克
            _ => "".to_string(),
        };
        info!("get_pre_deal_amount key:{}", contract_no1.clone());

        if contract_no1.is_empty() {
            error!("市场代码不存在");
            return Ok(res);
        }

        //已经生成的
        let ret = self.kline_client.write().await.get_generate_fenshi_hq(&contract_no1, &"1".to_string()).await;
        if ret.as_ref().is_err() {
            error!("{:?}", &ret);
            return Ok(res);
        }
        let fenshi_val = ret.unwrap();
        info!("分时: {}", &fenshi_val.fenshi_hq);
        // let vec: Vec<&str> = fenshi_val.fenshi_hq.rsplitn(3, "|").collect();//当前周期的上一周期

        let val: Vec<&str> = fenshi_val.fenshi_hq.rsplitn(2, req.time.as_str()).collect();
        if val.is_empty() || val.len() != 2 {
            return Ok(res);
        }
        let vec: Vec<&str> = val[1].rsplitn(3, "|").collect();
        info!("获取{}点成交量：{}", req.time, vec[1]);
        res.prev_period_amount = vec[1].parse().unwrap_or_default();
        Ok(res)
    }

    async fn get_history_tick(&self, code: &String, time: i64, value: &mut String) -> Result<()> {
        let mut tick = HqTick::new();
        let strat_time = format!("{}{}", (time - 5), "000");
        let end_time = format!("{}{}", (time - 5), "000");
        info!("get_history_tick code {} starttime:{} endtime: {}", &code, &strat_time, &end_time);

        let st = from_timestamp_to_naive_date_time(time);
        info!("trade_day: {}", st.to_string());

        let cql = format!(
            "select vc_content from tstockhqtick \
            where vc_contract_code = '{}' and  l_update_time  > '{}'  and l_update_time < '{}' ALLOW FILTERING;",
            code, strat_time, end_time
        );
        info!("cql: {}", &cql);
        let query_ret = self.cassandra_client.query_cass(&cql, code).await;
        if let Some(rows_result) = query_ret {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            while let Some(vc_content) = rows_iter.next().transpose()? {
                tick.vc_content = vc_content.0;
                if tick.vc_content.contains(&st.to_string()) {
                    continue;
                }
                let str_tmp_value: String = tick.vc_content.clone();
                let _ = write!(value, "{}", str_tmp_value);
            }

            // for row in rows.into_typed::<(String,)>() {
            //     match row {
            //         Ok((vc_content,)) => {
            //             tick.vc_content = vc_content;
            //             if tick.vc_content.contains(&st.to_string()) {
            //                 continue;
            //             }
            //             let str_tmp_value: String = tick.vc_content.clone();
            //             let _ = write!(value, "{}", str_tmp_value);
            //         }
            //         Err(err) => {
            //             error!("{:#?}", err)
            //         }
            //     }
            // }
        } else {
            return Err(anyhow!("{:?}", "未查到tick数据"));
        }
        Ok(())
    }

    async fn get_delay_tick(&self, code: &String) -> Result<YsHqInfo> {
        let mut tick = HqTick::new(); //cassandra tick value
        let mut st = current_naive_time();
        let mut tt = st.and_utc().timestamp() - 15 * 60;

        st = from_timestamp_to_naive_date_time(tt);
        info!("time: {}", st.to_string());

        if st.hour() >= 16 {
            //收盘后取16点的tick
            if code.contains("XHKG") {
                tt = tt - (st.hour() as i64 - 16) * 3600 - st.minute() as i64 * 60 - st.second() as i64;
            }
        }
        if !code.contains("XSHG") && !code.contains("XSHE") && !code.contains("XHKG") { //美股
             //暂时不处理
        }

        st = from_timestamp_to_naive_date_time(tt);
        let llt = utility::timeutil::convert_datetime_to_timestamp(&st.to_string());
        let cql = format!(
            "select vc_content from tstockhqtick \
            where vc_contract_code = '{}' and l_update_time < '{}' ORDER BY l_update_time DESC limit 1 ALLOW FILTERING;",
            code, llt
        );
        info!("cql: {}", cql);

        let mut hqtick = YsHqInfo::default();
        let query_ret = self.cassandra_client.query_cass(&cql, code).await;
        if let Some(rows_result) = query_ret {
            let mut rows_iter = rows_result.rows::<(String,)>()?;
            while let Some(vc_content) = rows_iter.next().transpose()? {
                tick.vc_content = vc_content.0;
                anti_serialize_tick(&mut hqtick, &tick.vc_content).await
            }
            // info!("query len: {}", &rows.len());
            // for row in rows.into_typed::<(String,)>() {
            //     match row {
            //         Ok((vc_content,)) => {
            //             tick.vc_content = vc_content;
            //             anti_serialize_tick(&mut hqtick, &tick.vc_content).await
            //         }
            //         Err(err) => {
            //             error!("{:?}", err);
            //             return Ok(hqtick);
            //         }
            //     }
            // }
        } else {
            info!("Query Cassandra None");
        }
        Ok(hqtick)
    }

    //获取历史分时数据
    pub async fn get_history_time_share(&self, code: &String) -> Result<KLineHqResp> {
        info!("获取 {} 历史分时", &code);
        let mut resp = KLineHqResp::default();
        let mut kline_info = KLineHqInfo::default();
        let mut value: String = String::default();
        {
            let ch = code.to_uppercase();
            //let ch = code.to_case(Case::Upper);
            let cql = format!(
                "select vc_content from tstockhqtimeshare \
                where vc_code = 'STOCK_FS:{}' ORDER BY l_update_time DESC limit 1 ALLOW FILTERING;",
                ch
            );
            info!("get_history_time_share query cql:{}", cql);
            let query_ret = self.cassandra_client.query_cass(&cql, code).await;
            if let Some(rows_result) = query_ret {
                let mut rows_iter = rows_result.rows::<(String,)>()?;
                while let Some(vc_content) = rows_iter.next().transpose()? {
                    let str_tmp_value = vc_content.0;
                    let _ = write!(value, "{}", str_tmp_value);
                }

                // info!("query len: {}", &rows.len());
                // for row in rows.into_typed::<(String,)>() {
                //     match row {
                //         Ok((vc_content,)) => {
                //             let str_tmp_value = vc_content;
                //             let _ = write!(value, "{}", str_tmp_value);
                //         }
                //         Err(err) => {
                //             error!("{:?}", err);
                //             return Ok(resp);
                //         }
                //     }
                // }
            } else {
                info!("Query Cassandra None");
            }
        }
        info!("get_history_time_share klineValue:{}", value);
        if !value.is_empty() {
            let ret = PhoenixHqcenterController::convert_fs_to_local_time(&self, value).await;
            if ret.as_ref().is_err() {
                error!("{:?}", &ret);
                return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
            }
            value = ret.unwrap();
        }
        kline_info.strkline = value;
        resp.klineinfo.push(kline_info);

        Ok(resp)
    }

    //接口取最新价
    pub async fn get_tick_price(&self, req: &LastPriceMsgReq) -> Result<LastPriceMsgResp> {
        info!("get_tick_price StockCode:{} ExchangeId:{}", req.stock_code, req.exchange_id);
        let mut res = LastPriceMsgResp::default();
        res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).0;
        res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeOk).1;
        let mut last_price_info = LastPriceInfo::default();

        let contract_no1 = match req.exchange_id {
            101 => req.stock_code.clone() + "_XSHG",
            102 => req.stock_code.clone() + "_XSHE",
            103 => req.stock_code.clone() + "_XHKG", //港交所
            104 => req.stock_code.clone() + "_XASE", //美交所
            105 => req.stock_code.clone() + "_XNYS", //纽交所
            106 => req.stock_code.clone() + "_XNAS", //纳斯达克
            _ => "".to_string(),
        };
        info!("stock_code: {}", contract_no1);

        if contract_no1.is_empty() {
            res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeExchangeNONotexist).0;
            res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeExchangeNONotexist).1;
            error!("市场代码不存在");
            return Ok(res);
        }

        if contract_no1.contains("_XASE") || contract_no1.contains("_XNYS") || contract_no1.contains("_XNAS") {
            match self.us_client.write().await.query_us_last_price(&contract_no1).await {
                Ok(data) => {
                    info!("query us last price: {:?}", data);
                    last_price_info.last_price = data.last_price; //最新价
                    last_price_info.change_value = data.change_value; //涨跌值
                    last_price_info.change_rate = data.change_rate; //涨跌幅
                    info!(
                        "us stock_code: {}, last_price: {}, change_value: {}, change_rate: {}",
                        contract_no1, last_price_info.last_price, last_price_info.change_value, last_price_info.change_rate
                    );
                }
                Err(err) => {
                    error!("query us last price err: {:?}", err);
                    res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).0;
                    res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).1;
                    return Ok(res);
                }
            }
        } else {
            let ret = self.tick_client.write().await.get_last_price(&contract_no1).await;
            if ret.as_ref().is_err() || ret.as_ref().unwrap().data.is_none() {
                error!("get last price err: {:?}", ret);
                res.err_msg = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).0;
                res.err_code = hqerrors::get_error_code(hqerrors::ErrorCode::CodeRedisNoData).1;
                return Ok(res);
            }
            let data = ret.unwrap().data.unwrap();
            info!("query hs or hk last price: {:?}", data);
            last_price_info.last_price = data.q_last_price; //最新价
            last_price_info.change_value = data.q_change_value; //涨跌值
            last_price_info.change_rate = data.q_change_rate; //涨跌幅
            info!(
                "stock_code: {}, last_price: {}, change_value: {}, change_rate: {}",
                contract_no1, last_price_info.last_price, last_price_info.change_value, last_price_info.change_rate
            );
        }
        res.data = Some(last_price_info);
        info!("ret value: {:?}", res);
        Ok(res)
    }

    pub async fn get_tick_hq(&self, req: &TickHqReq) -> Result<TickHqResp> {
        let mut resp = TickHqResp::default();
        if req.ticktime == 0 {
            //实时tick
            let mut stock_codes: Vec<&str> = req.strcontractno.split(',').collect();
            if let Some(index) = stock_codes.iter_mut().position(|x| x.is_empty()) {
                stock_codes.remove(index);
            }
            let mut strcontractno_hs = String::new();
            let mut strcontractno_hk = String::new();
            let mut strcontractno_us = Vec::new();
            for stock_code in stock_codes.iter() {
                if stock_code.contains("XSHE") || stock_code.contains("XSHG") || stock_code.contains("HS") {
                    let _ = write!(strcontractno_hs, "{},", stock_code);
                } else if stock_code.contains("XHKG") || stock_code.contains("HK") {
                    let _ = write!(strcontractno_hk, "{},", stock_code);
                } else {
                    strcontractno_us.push(stock_code.to_string());
                }
            }

            let mut ticks_hs: Vec<YsHqInfo> = Vec::new();
            let mut ticks_hk: Vec<YsHqInfo> = Vec::new();
            let mut ticks_us: Vec<YsHqInfo> = Vec::new();
            if !strcontractno_hs.is_empty() {
                match self.tick_client.write().await.get_latest_tick(&strcontractno_hs).await {
                    Ok(ret) => ticks_hs = ret.tick_hq_info,
                    Err(err) => error!("get hs tick err: {:?}", &err),
                }
            }
            if !strcontractno_hk.is_empty() {
                match self.tick_client.write().await.get_latest_tick(&strcontractno_hk).await {
                    Ok(ret) => ticks_hk = ret.tick_hq_info,
                    Err(err) => error!("get hk tick err: {:?}", &err),
                }
            }
            if !strcontractno_us.is_empty() {
                //美股
                match self.us_client.write().await.query_us_tick(&strcontractno_us).await {
                    Ok(ret) => ticks_us = ret,
                    Err(err) => error!("get us tick err: {:?}", &err),
                }
            }
            ticks_hs.append(&mut ticks_hk);
            ticks_hs.append(&mut ticks_us);

            info!("tick len {}", &ticks_hs.len());
            for val in ticks_hs.iter() {
                let mut hq_info: YsHqInfo; // = YsHqInfo::default();
                if req.realtime == 0 && !val.contract_no1.contains("XSHE") && !val.contract_no1.contains("XSHG") {
                    //延时行情
                    let mut st = current_naive_time();
                    let tt = st.and_utc().timestamp() - 15 * 60;

                    st = from_timestamp_to_naive_date_time(tt);
                    info!("code: {}, {}, {}", val.contract_no1, val.tapidtstamp, &st.to_string());

                    if val.tapidtstamp < st.to_string() {
                        hq_info = val.to_owned();
                    } else {
                        let ret = self.get_delay_tick(&val.contract_no1).await;
                        if ret.as_ref().is_err() {
                            error!("{:?}", &ret);
                            // return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                            continue;
                        }

                        hq_info = ret.unwrap();
                        let size = hq_info.q_bid_price.len();
                        for index in 0..size {
                            hq_info.q_bid_price[index] = 0.0;
                            hq_info.q_ask_price[index] = 0.0;
                            hq_info.q_bid_qty[index] = 0;
                            hq_info.q_ask_qty[index] = 0;
                        }
                        hq_info.contract_no1 = val.contract_no1.clone();
                    }
                } else {
                    hq_info = val.to_owned();
                }

                if req.iticktype != 1 {
                    // 1 5档 其它 1档
                    let size = hq_info.q_bid_price.len();
                    for index in 0..size {
                        hq_info.q_bid_price[index] = 0.0;
                        hq_info.q_ask_price[index] = 0.0;
                        hq_info.q_bid_qty[index] = 0;
                        hq_info.q_ask_qty[index] = 0;
                    }
                }
                resp.tickhqinfo.push(hq_info);
            }
        } else {
            // 历史TICK
            let mut value = String::new();
            let ret = self.get_history_tick(&req.strcontractno, req.ticktime, &mut value).await;
            if ret.as_ref().is_err() {
                error!("{:?}", &ret);
            }
            info!("历史tick数据");
        }
        info!("{:?}", resp);
        Ok(resp)
    }

    pub async fn get_last_kline_data(&self, contract_no: &String, kline_type: &String) -> Result<String> {
        let ret = self.kline_client.write().await.get_last_kline_data(contract_no, kline_type).await;
        if ret.as_ref().is_err() {
            error!("{:?}", &ret);
            return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
        }
        let kline_data = ret.unwrap();
        if kline_data.stock_code.is_empty() {
            info!("未找到最新K线数据: {:?}", &kline_data);
            return Ok("".to_string());
        }
        let minutes = (kline_data.prev_minutes + kline_data.period) % 1440;

        let st = build_naive_date_time(&kline_data.tick_time);
        //年月日时分
        let time = format!("{:04}{:02}{:02}{:02}{:02}00", &st.year(), &st.month(), &st.day(), minutes / 60, minutes % 60);
        //字符串类型 收盘价|最高价|最新价|最低价|开盘价|成交量|时间戳|交易额
        let kline_value = format!(
            "{:<.03}|{:<.03}|{:<.03}|{:<.03}|{:<.03}|{}|{}|{:<.03}",
            kline_data.close_price, kline_data.high_price, kline_data.last_price, kline_data.low_price, kline_data.open_price, kline_data.current_period_volume, time, kline_data.current_period_turnover
        );
        Ok(kline_value)
    }

    //转换本地时间
    pub async fn convert_to_local_time(&self, ll: i64) -> Result<i64> {
        let mut tmp = ll;
        if self.settings.common.time_diff == 0 {
            return Ok(ll);
        }
        let second = (tmp % 100) as u32; //秒
        tmp /= 100;
        let minute = (tmp % 100) as u32; //分
        tmp /= 100;
        let hour = (tmp % 100) as u32; //小时
        tmp /= 100;
        let day = (tmp % 100) as u32; //天
        tmp /= 100;
        let month = (tmp % 100) as u32; //月
        tmp /= 100;
        let year = tmp as i32; //年

        //2021-11-24 22:02:08
        let date_time = format!("{:0>4}-{:0>2}-{:0>2} {:0>2}:{:0>2}:{:0>2}", year, month, day, hour, minute, second);
        //2021-11-24 22:02:08
        let local_time = build_naive_date_time(&date_time);

        let tt = local_time.and_utc().timestamp() + (self.settings.common.time_diff * 60 * 60) as i64;

        let local_time = from_timestamp_to_naive_date_time(tt);
        let time: i64 = (local_time.year() as i64) * 10000000000 + (local_time.month() as i64) * 100000000 + (local_time.day() as i64) * 1000000 + (local_time.hour() as i64) * 10000 + (local_time.minute() as i64) * 100;
        Ok(time)
    }

    async fn convert_fs_to_local_time(&self, val: String) -> Result<String> {
        if self.settings.common.time_diff == 0 {
            return Ok(val);
        }
        let mut value: Vec<&str> = Vec::new();
        //4.160|4.150|4.150|0|20210323093000+4.147|4.150|284|20210323093100+
        //4.148|4.160|24|20210323093200+4.148|4.150|9|20210323093400+................
        if !val.is_empty() {
            if val.contains("+") {
                value = val.split("+").collect(); //按+分割为一周期
            }
        }
        if value.is_empty() {
            return Ok(val);
        }
        let mut val_tmp = String::default();
        for i in value.iter() {
            if let Some(index) = i.rfind('|') {
                //后面开始找日期
                let update_time: i64 = i[index + 1..].to_string().parse().unwrap_or_default(); //由|分割开的最后一个值
                let ret = PhoenixHqcenterController::convert_to_local_time(&self, update_time).await;
                if ret.as_ref().is_err() {
                    error!("{:?}", &ret);
                    return Err(anyhow!("{:?}", &ret.as_ref().err().unwrap().to_string()));
                }
                let local_time = ret.unwrap().to_string();
                let mut str_tmp = i.trim_end_matches(&update_time.to_string()).to_string(); //删除末尾的字符串片段:4.160|4.150|4.150|0|
                str_tmp.push_str(&local_time);
                val_tmp.push_str(&str_tmp);
                val_tmp.push('+');
            }
        }
        Ok(val_tmp)
    }
}
