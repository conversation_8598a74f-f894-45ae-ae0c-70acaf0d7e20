use std::collections::HashMap;
// use sled::Db;
// use std::fmt::Write;
use anyhow::Result;
use chrono::prelude::*;

use utility::timeutil::build_naive_date_time;

// use crate::client::sledclient::SledClient;
use super::common::KLineData;
use crate::client::cassandraclient::CassandraClient;

pub struct PersistService {}

/// 行情数据
#[derive(scylla::SerializeRow, Clone, Debug)]
pub struct CassandraData {
    pub key: String,
    pub uptime: String,
    pub val: String,
}

/// K线数据
#[derive(scylla::SerializeRow, Clone, Debug, serde::Serialize, serde::Deserialize, Default)]
pub struct CassandraKLineData {
    pub peroid: i32,
    pub vc_code: String,
    pub l_update_time: String,
    pub en_close_price: f64,
    pub en_high_price: f64,
    pub en_last_price: f64,
    pub en_low_price: f64,
    pub en_open_price: f64,
    pub l_volume: f64,
}

/// 分时数据
#[derive(scylla::SerializeRow, Clone, Debug)]
pub struct FenShiData {
    pub vc_code: String,
    pub l_update_time: i64,
    pub vc_content: String,
}

impl PersistService {
    pub fn get_table_name(period: i32) -> String {
        if period == 1 {
            "tstockhq1kline".to_string()
        } else if period == 5 {
            "tstockhq5kline".to_string()
        } else if period == 10 {
            "tstockhq10kline".to_string()
        } else if period == 30 {
            "tstockhq30kline".to_string()
        } else if period == 60 {
            "tstockhq60kline".to_string()
        } else {
            //1440
            "tstockhqdaykline".to_string()
        }
    }

    #[allow(dead_code)]
    pub async fn insert_klines_into_cassandra(klines: &Vec<KLineData>, cass_client: &CassandraClient) -> Result<()> {
        let contents: Vec<CassandraKLineData> = klines
            .iter()
            .map(|x| {
                let st = build_naive_date_time(&x.tick_time);
                let minutes = if x.period == 1440 {
                    x.end_minutes
                } else {
                    (x.prev_minutes + x.period + 1440) % 1440 // 保证为正
                };
                CassandraKLineData {
                    peroid: x.period,
                    vc_code: x.stock_code.clone(),
                    l_update_time: format!("{:04}{:02}{:02}{:02}{:02}00", &st.year(), &st.month(), &st.day(), minutes / 60, minutes % 60),
                    en_close_price: x.close_price,
                    en_high_price: x.high_price,
                    en_last_price: x.last_price,
                    en_low_price: x.low_price,
                    en_open_price: x.open_price,
                    l_volume: x.current_period_volume,
                }
            })
            .collect();

        cass_client.batch_insert_kline_per_table(&contents).await
    }

    #[allow(dead_code)]
    pub async fn insert_fenshi_into_cassandra(fenshi: &HashMap<String, String>, cass_client: &CassandraClient) -> Result<()> {
        let contents: Vec<FenShiData> = fenshi
            .iter()
            .filter_map(|(k, v)| {
                let mut parts = k.split('|'); // let str: Vec<&str> = k.split("|").collect(); //[STOCK_FS:600000_XSHG, 20220823]
                let vc_code = parts.next()?.to_owned(); // str[0].to_owned()
                let l_update_time = parts.next().and_then(|s| s.parse::<i64>().ok()).unwrap_or_default(); //str[1].parse::<i64>().unwrap_or_default()
                Some(FenShiData {
                    vc_code,
                    l_update_time,
                    vc_content: v.clone(),
                })
            })
            .collect();

        let cql = "insert into tstockhqtimeshare (vc_code, l_update_time, vc_content) values (?, ?, ?)";
        cass_client.execute_batch_fenshi(cql, &contents).await
        // let prepared = cass_client.do_prepare(cql).await.map_err(|e| anyhow!("prepare error: {}", e))?;

        // for val in &contents {
        //     cass_client.execute_prepare(&prepared, val).await.map_err(|e| anyhow!("insert cassandra error: {}", e))?;
        // }
        // Ok(())
    }
}
