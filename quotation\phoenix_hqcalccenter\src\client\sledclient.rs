use chrono::Datelike;
// use chrono::Datelike;
use sled::{Db, Tree};
// use sled::Batch;
// use std::str::FromStr;
use anyhow::Result;
// use utility::timeutil::build_naive_date_time;
use std::collections::HashMap;
use tracing::info;

use crate::server::service::{common::KLineData, persistservice::CassandraKLineData};

// use crate::server::service::{
//     common::KLineData, persistservice::CassandraKLineData,
// };
#[allow(dead_code)]
pub struct SledClient {
    pub db: Db,
    pub kline_1: Tree,
    pub kline_5: Tree,
    pub kline_10: Tree,
    pub kline_30: Tree,
    pub kline_60: Tree,
    pub kline_day: Tree,
}

impl SledClient {
    #[allow(dead_code)]
    pub fn new(path: &String) -> Self {
        let db = sled::open(path).expect("打开文件失败");
        let kline_1 = db.open_tree(b"kline_1").expect("打开文件失败");
        let kline_5 = db.open_tree(b"kline_5").expect("打开文件失败");
        let kline_10 = db.open_tree(b"kline_10").expect("打开文件失败");
        let kline_30 = db.open_tree(b"kline_30").expect("打开文件失败");
        let kline_60 = db.open_tree(b"kline_60").expect("打开文件失败");
        let kline_day = db.open_tree(b"kline_day").expect("打开文件失败");
        Self {
            db,
            kline_1,
            kline_5,
            kline_10,
            kline_30,
            kline_60,
            kline_day,
        }
        // Self { db }
    }

    #[allow(dead_code)]
    pub async fn read_fenshi(&self) -> HashMap<String, String> {
        let mut map: HashMap<String, String> = HashMap::new();
        info!("文件读取分时数据....");
        for val in self.db.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let str = format!("{}", String::from_utf8(v.to_vec()).unwrap()).trim().to_string();
                // info!("{}-----------{}", key, str);
                map.insert(key, str)
            });
        }
        // let _ = self.db.clear();
        info!("文件读取{}条分时数据", map.len());
        map
    }

    #[allow(dead_code)]
    pub async fn batch_insert_fs(&self, fenshis: &HashMap<String, String>) -> Result<()> {
        // let mut batch = sled::Batch::default();
        // for (key, val) in fenshis.iter() {
        //     // info!("key: {}, val: {}", &key, &val);
        //     // let value = serde_json::to_string(&val).unwrap(); //序列化为json字符串
        //     batch.insert(key.as_str(), val.as_str());
        // }
        // if let Err(err) = self.db.apply_batch(batch) {
        //     return Err(anyhow!("{}", err));
        // }
        // Ok(())

        let db = self.db.clone();
        let fenshis = fenshis.clone(); // 需要 HashMap 实现 Clone

        tokio::task::spawn_blocking(move || {
            let mut batch = sled::Batch::default();
            for (key, val) in fenshis.iter() {
                batch.insert(key.as_str(), val.as_str());
            }
            db.apply_batch(batch).map_err(|err| anyhow::anyhow!("{}", err))
        })
        .await?
    }

    pub async fn file_cache(&self, kline: &KLineData) {
        let kline_data = CassandraKLineData {
            peroid: kline.period,
            vc_code: kline.stock_code.clone(),
            l_update_time: {
                let tick_time = kline.tick_time.clone();
                let st = utility::timeutil::build_naive_date_time(&tick_time);

                let minutes = if kline.period == 1440 { kline.end_minutes } else { (kline.prev_minutes + kline.period) % 1440 };
                //年月日时分
                format!("{:04}{:02}{:02}{:02}{:02}00", &st.year(), &st.month(), &st.day(), minutes / 60, minutes % 60)
            },
            en_close_price: kline.close_price,
            en_high_price: kline.high_price,
            en_last_price: kline.last_price,
            en_low_price: kline.low_price,
            en_open_price: kline.open_price,
            l_volume: kline.current_period_volume,
        };
        let value = serde_json::to_string(&kline_data).unwrap(); //序列化为json字符串
                                                                 // info!("{}", value);
        let key = format!("{}|{}", kline_data.vc_code, kline_data.l_update_time);
        if kline.period == 1 {
            let _ = self.kline_1.insert(&key, value.as_str());
            // match self.kline_1.get(&kline_data.vc_code) {
            //     Ok(Some(val)) => {
            //         let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //         let _ = self.kline_1.insert(&kline_data.vc_code, str.as_str());
            //     }
            //     Ok(None) => {
            //         let _ = self.kline_1.insert(&kline_data.vc_code, value.as_str());
            //     }
            //     Err(e) => {
            //         error!("operational problem encountered: {}", e);
            //         return;
            //     }
            // }
        } else if kline.period == 5 {
            let _ = self.kline_5.insert(&key, value.as_str());
            // match self.kline_5.get(&kline_data.vc_code) {
            //     Ok(Some(val)) => {
            //         let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //         let _ = self.kline_5.insert(&kline_data.vc_code, str.as_str());
            //     }
            //     Ok(None) => {
            //         let _ = self.kline_5.insert(&kline_data.vc_code, value.as_str());
            //     }
            //     Err(e) => {
            //         error!("operational problem encountered: {}", e);
            //         return;
            //     }
            // }
        } else if kline.period == 10 {
            let _ = self.kline_10.insert(&key, value.as_str());
            // let s = self.kline_10.get(&kline_data.vc_code).unwrap();
            // if s.is_none() {
            //     let _ = self.kline_10.insert(&kline_data.vc_code, value.as_str());
            // } else {
            //     let val = s.unwrap();
            //     let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //     // info!("{}", str);
            //     let _ = self.kline_10.insert(&kline_data.vc_code, str.as_str());
            // }
        } else if kline.period == 30 {
            let _ = self.kline_30.insert(&key, value.as_str());
            // let s = self.kline_30.get(&kline_data.vc_code).unwrap();
            // if s.is_none() {
            //     let _ = self.kline_30.insert(&kline_data.vc_code, value.as_str());
            // } else {
            //     let val = s.unwrap();
            //     let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //     // info!("{}", str);
            //     let _ = self.kline_30.insert(&kline_data.vc_code, str.as_str());
            // }
        } else if kline.period == 60 {
            let _ = self.kline_60.insert(&key, value.as_str());
            // let s = self.kline_60.get(&kline_data.vc_code).unwrap();
            // if s.is_none() {
            //     let _ = self.kline_60.insert(&kline_data.vc_code, value.as_str());
            // } else {
            //     let val = s.unwrap();
            //     let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //     // info!("{}", str);
            //     let _ = self.kline_60.insert(&kline_data.vc_code, str.as_str());
            // }
        } else {
            let _ = self.kline_day.insert(&key, value.as_str());
            // let s = self.kline_day.get(&kline_data.vc_code).unwrap();
            // if s.is_none() {
            //     let _ = self.kline_day.insert(&kline_data.vc_code, value.as_str());
            // } else {
            //     let val = s.unwrap();
            //     let str = format!("{}+{}", String::from_utf8(val.to_vec()).unwrap(), value).trim().to_string();
            //     let _ = self.kline_day.insert(&kline_data.vc_code, str.as_str());
            // }
        }
    }
    #[allow(dead_code)]
    pub async fn read_kline(&self) -> HashMap<String, CassandraKLineData> {
        let mut map: HashMap<String, CassandraKLineData> = HashMap::new();
        // match self.kline_1.get("600000_XSHG") {
        //     Ok(Some(value)) => {
        //         info!("retrieved value {}", String::from_utf8(value.to_vec()).unwrap());
        //         let str = String::from_utf8(value.to_vec()).unwrap();
        //         let kline: Vec<&str> = str.split("+").collect();
        //         info!("{:#?}", kline);
        //         let s = kline.iter().map(|x| serde_json::from_str::<CassandraKLineData>(&x).expect("kline err...")).collect::<Vec<CassandraKLineData>>();
        //         info!("{:#?}", s);
        //     }
        //     Ok(None) => info!("value not found"),
        //     Err(e) => info!("operational problem encountered: {}", e),
        // }

        for val in self.kline_1.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                // let str = format!("{}", String::from_utf8(v.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                // let kline = serde_json::from_value::<CassandraKLineData>(serde_json::Value::String(str)).unwrap_or_default();
                // info!("{}-----------{}", key, str);
                map.insert(key, kline)
            });
        }
        for val in self.kline_5.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                map.insert(key, kline)
            });
        }
        for val in self.kline_10.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                map.insert(key, kline)
            });
        }
        for val in self.kline_30.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                map.insert(key, kline)
            });
        }
        for val in self.kline_60.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                map.insert(key, kline)
            });
        }
        for val in self.kline_day.iter() {
            let _ret = val.map(|(k, v)| {
                let key = format!("{}", String::from_utf8(k.to_vec()).unwrap()).trim().to_string();
                let kline = serde_json::from_str::<CassandraKLineData>(&String::from_utf8(v.to_vec()).unwrap()).unwrap_or_default();
                map.insert(key, kline)
            });
        }

        info!("文件读取{}条K线数据", map.len());
        map
    }
}
